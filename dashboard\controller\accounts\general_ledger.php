<?php
/**
 * تحكم دفتر الأستاذ العام الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsGeneralLedger extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') || 
            !$this->user->hasKey('accounting_general_ledger_view')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts', 
                'محاولة وصول غير مصرح بها لدفتر الأستاذ العام', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/general_ledger.css');
        $this->document->addScript('view/javascript/accounts/general_ledger.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts', 
            'عرض شاشة دفتر الأستاذ العام', [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/general_ledger'
        ]);

        $this->getForm();
    }

    /**
     * توليد دفتر الأستاذ العام المتقدم
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') || 
            !$this->user->hasKey('accounting_general_ledger_generate')) {
            
            $this->central_service->logActivity('unauthorized_generate', 'accounts', 
                'محاولة توليد دفتر أستاذ عام غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'generate_general_ledger'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->load->model('accounts/general_ledger');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts', 
                    'توليد دفتر الأستاذ العام للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'account_id' => $filter_data['account_id'] ?? 'all'
                ]);

                $ledger_data = $this->model_accounts_general_ledger->generateGeneralLedger($filter_data);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'general_ledger_generated', 
                    'توليد دفتر الأستاذ العام', 
                    'تم توليد دفتر الأستاذ العام للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'] . ' بواسطة ' . $this->user->getFirstName(), 
                    [$this->config->get('config_chief_accountant_id')], 
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_entries' => $ledger_data['totals']['total_entries'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['general_ledger_data'] = $ledger_data;

                $this->response->redirect($this->url->link('accounts/general_ledger/view', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض دفتر الأستاذ العام
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') || 
            !$this->user->hasKey('accounting_general_ledger_view')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['general_ledger_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['general_ledger_data'];
        
        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts', 
            'عرض دفتر الأستاذ العام', [
            'user_id' => $this->user->getId(),
            'action' => 'view_general_ledger'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/general_ledger/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/general_ledger/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/general_ledger/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/general_ledger_view', $data));
    }

    /**
     * تصدير دفتر الأستاذ العام
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') || 
            !$this->user->hasKey('accounting_general_ledger_export')) {
            
            $this->central_service->logActivity('unauthorized_export', 'accounts', 
                'محاولة تصدير دفتر أستاذ عام غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'export_general_ledger'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->load->model('accounts/general_ledger');

        if (!isset($this->session->data['general_ledger_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = isset($this->request->get['format']) ? $this->db->escape($this->request->get['format']) : 'excel';
        $ledger_data = $this->session->data['general_ledger_data'];
        $filter_data = $this->session->data['general_ledger_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts', 
            'تصدير دفتر الأستاذ العام - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'general_ledger_exported', 
            'تصدير دفتر الأستاذ العام', 
            'تم تصدير دفتر الأستاذ العام بصيغة ' . strtoupper($format) . ' بواسطة ' . $this->user->getFirstName(), 
            [$this->config->get('config_chief_accountant_id')], 
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($ledger_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($ledger_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($ledger_data, $filter_data);
                break;
            default:
                $this->exportToExcel($ledger_data, $filter_data);
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/general_ledger')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => isset($this->request->post['date_start']) ? $this->db->escape($this->request->post['date_start']) : date('Y-01-01'),
            'date_end' => isset($this->request->post['date_end']) ? $this->db->escape($this->request->post['date_end']) : date('Y-m-d'),
            'account_id' => isset($this->request->post['account_id']) ? (int)$this->request->post['account_id'] : 0,
            'account_group' => isset($this->request->post['account_group']) ? $this->db->escape($this->request->post['account_group']) : '',
            'include_zero_balances' => isset($this->request->post['include_zero_balances']) ? 1 : 0,
            'show_opening_balance' => isset($this->request->post['show_opening_balance']) ? 1 : 0,
            'show_running_balance' => isset($this->request->post['show_running_balance']) ? 1 : 0,
            'group_by_account' => isset($this->request->post['group_by_account']) ? 1 : 0,
            'currency' => isset($this->request->post['currency']) ? $this->db->escape($this->request->post['currency']) : $this->config->get('config_currency'),
            'branch_id' => isset($this->request->post['branch_id']) ? (int)$this->request->post['branch_id'] : 0
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/general_ledger/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('accounts/chartaccount');
        $this->load->model('branch/branch');

        $data['accounts'] = $this->model_accounts_chartaccount->getAccounts();
        $data['account_groups'] = $this->model_accounts_chartaccount->getAccountGroups();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['account_id'] = $this->request->post['account_id'] ?? '';
        $data['include_zero_balances'] = $this->request->post['include_zero_balances'] ?? false;
        $data['show_opening_balance'] = $this->request->post['show_opening_balance'] ?? true;
        $data['show_running_balance'] = $this->request->post['show_running_balance'] ?? true;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/general_ledger_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'general_ledger_' . $filter_data['date_start'] . '_to_' . $filter_data['date_end'] . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="6">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_account_code') . '</th>';
        echo '<th>' . $this->language->get('text_account_name') . '</th>';
        echo '<th>' . $this->language->get('text_debit') . '</th>';
        echo '<th>' . $this->language->get('text_credit') . '</th>';
        echo '<th>' . $this->language->get('text_balance') . '</th></tr>';

        // إضافة البيانات
        foreach ($data['accounts'] as $account) {
            echo '<tr>';
            echo '<td>' . $account['code'] . '</td>';
            echo '<td>' . $account['name'] . '</td>';
            echo '<td>' . $account['total_debits'] . '</td>';
            echo '<td>' . $account['total_credits'] . '</td>';
            echo '<td>' . $account['closing_balance'] . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        // إضافة البيانات
        $pdf->SetFont('dejavusans', '', 8);
        foreach ($data['accounts'] as $account) {
            $pdf->Cell(30, 6, $account['code'], 1);
            $pdf->Cell(60, 6, $account['name'], 1);
            $pdf->Cell(30, 6, $account['total_debits'], 1);
            $pdf->Cell(30, 6, $account['total_credits'], 1);
            $pdf->Cell(30, 6, $account['closing_balance'], 1);
            $pdf->Ln();
        }

        $pdf->Output('general_ledger_' . $filter_data['date_start'] . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'general_ledger_' . $filter_data['date_start'] . '_to_' . $filter_data['date_end'] . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_account_code'),
            $this->language->get('text_account_name'),
            $this->language->get('text_debit'),
            $this->language->get('text_credit'),
            $this->language->get('text_balance')
        ));

        // إضافة البيانات
        foreach ($data['accounts'] as $account) {
            fputcsv($output, array(
                $account['code'],
                $account['name'],
                $account['total_debits'],
                $account['total_credits'],
                $account['closing_balance']
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * طباعة دفتر الأستاذ العام
     */
    public function print() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') ||
            !$this->user->hasKey('accounting_general_ledger_print')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة طباعة دفتر الأستاذ العام غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->load->model('accounts/general_ledger');

        // الحصول على البيانات من الجلسة أو المعاملات
        $filter_data = array();

        if (isset($this->request->get['date_start'])) {
            $filter_data['date_start'] = $this->request->get['date_start'];
        } else {
            $filter_data['date_start'] = date('Y-m-01');
        }

        if (isset($this->request->get['date_end'])) {
            $filter_data['date_end'] = $this->request->get['date_end'];
        } else {
            $filter_data['date_end'] = date('Y-m-d');
        }

        if (isset($this->request->get['account_id'])) {
            $filter_data['account_id'] = $this->request->get['account_id'];
        }

        if (isset($this->request->get['account_type'])) {
            $filter_data['account_type'] = $this->request->get['account_type'];
        }

        try {
            // تسجيل طباعة دفتر الأستاذ
            $this->central_service->logActivity('print_general_ledger', 'accounts',
                'طباعة دفتر الأستاذ العام للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                'user_id' => $this->user->getId(),
                'date_start' => $filter_data['date_start'],
                'date_end' => $filter_data['date_end'],
                'account_id' => $filter_data['account_id'] ?? 'all'
            ]);

            // إنشاء دفتر الأستاذ
            $ledger_data = $this->model_accounts_general_ledger->generateGeneralLedger($filter_data);

            // إعداد بيانات الطباعة
            $data = $ledger_data;
            $data['date_start'] = $filter_data['date_start'];
            $data['date_end'] = $filter_data['date_end'];
            $data['currency'] = $this->config->get('config_currency');
            $data['company_name'] = $this->config->get('config_name');
            $data['company_logo'] = $this->config->get('config_logo') ?
                HTTP_SERVER . 'image/' . $this->config->get('config_logo') : '';
            $data['generated_date'] = date('Y-m-d H:i:s');
            $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

            // إرسال إشعار للإدارة المالية
            $this->central_service->sendNotification(
                'general_ledger_printed',
                'طباعة دفتر الأستاذ العام',
                'تم طباعة دفتر الأستاذ العام للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'],
                [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                [
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'total_accounts' => count($data['accounts']),
                    'total_transactions' => $data['summary']['total_transactions'],
                    'printed_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                ]
            );

            $this->response->setOutput($this->load->view('accounts/general_ledger_print', $data));

        } catch (Exception $e) {
            // في حالة الخطأ، إعادة توجيه مع رسالة خطأ
            $this->session->data['error'] = 'خطأ في طباعة دفتر الأستاذ: ' . $e->getMessage();
            $this->response->redirect($this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تحسين الأداء مع التخزين المؤقت
     */
    public function getCachedLedger($filter_data) {
        $cache_key = 'general_ledger_' . md5(serialize($filter_data));

        // محاولة الحصول على البيانات من التخزين المؤقت
        $cached_data = $this->cache->get($cache_key);

        if ($cached_data) {
            return $cached_data;
        }

        // إنشاء البيانات وحفظها في التخزين المؤقت
        $ledger_data = $this->model_accounts_general_ledger->generateGeneralLedger($filter_data);

        // حفظ لمدة 30 دقيقة
        $this->cache->set($cache_key, $ledger_data, 1800);

        return $ledger_data;
    }

    /**
     * تحليل مرئي لدفتر الأستاذ
     */
    public function visualAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/general_ledger') ||
            !$this->user->hasKey('accounting_general_ledger_analysis')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/general_ledger');
        $this->load->model('accounts/general_ledger');

        // إضافة مكتبات الرسوم البيانية
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addStyle('view/stylesheet/accounts/general_ledger_analysis.css');

        $filter_data = array(
            'date_start' => $this->request->get['date_start'] ?? date('Y-m-01'),
            'date_end' => $this->request->get['date_end'] ?? date('Y-m-d')
        );

        try {
            // الحصول على بيانات التحليل المرئي
            $analysis_data = $this->model_accounts_general_ledger->getVisualAnalysis($filter_data);

            $data['analysis'] = $analysis_data;
            $data['filter_data'] = $filter_data;

            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_visual_analysis'),
                'href' => $this->url->link('accounts/general_ledger/visualAnalysis', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['back'] = $this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true);

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('accounts/general_ledger_analysis', $data));

        } catch (Exception $e) {
            $this->session->data['error'] = 'خطأ في تحليل دفتر الأستاذ: ' . $e->getMessage();
            $this->response->redirect($this->url->link('accounts/general_ledger', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}
