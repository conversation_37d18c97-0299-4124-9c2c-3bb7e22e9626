<?php
class Controller<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Controller {
	public function index() {
		$data['title'] = $this->document->getTitle();

		if ($this->request->server['HTTPS']) {
			$data['base'] = HTTPS_SERVER;
		} else {
			$data['base'] = HTTP_SERVER;
		}

		if ($this->request->server['HTTPS']) {
            $server = HTTPS_CATALOG;
        } else {
            $server = HTTP_CATALOG;
        }

        if (is_file(DIR_IMAGE . $this->config->get('config_icon'))) {
			$this->document->addLink($server . 'image/' . $this->config->get('config_icon'), 'icon');
        }

		$data['description'] = $this->document->getDescription();
		$data['keywords'] = $this->document->getKeywords();
		$data['links'] = $this->document->getLinks();
		$data['styles'] = $this->document->getStyles();
		$data['scripts'] = $this->document->getScripts();
		$data['lang'] = $this->language->get('code');
		$data['direction'] = $this->language->get('direction');

		$this->load->language('common/header');

		$data['text_logged'] = sprintf($this->language->get('text_logged'), htmlspecialchars($this->user->getUserName(), ENT_QUOTES, 'UTF-8'));

		if (!isset($this->request->get['user_token']) || !isset($this->session->data['user_token']) || ($this->request->get['user_token'] != $this->session->data['user_token'])) {
			$data['logged'] = '';

			$data['home'] = $this->url->link('common/login', '', true);
		} else {
			$data['logged'] = true;

			$data['home'] = $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true);
			$data['logout'] = $this->url->link('common/logout', 'user_token=' . $this->session->data['user_token'], true);
			$data['profile'] = $this->url->link('common/profile', 'user_token=' . $this->session->data['user_token'], true);

			$this->load->model('user/user');

			$this->load->model('tool/image');

			$user_info = $this->model_user_user->getUser($this->user->getId());

			if ($user_info) {
				$data['firstname'] = $user_info['firstname'];
				$data['lastname'] = $user_info['lastname'];
				$data['username']  = $user_info['username'];
				$data['user_group'] = $user_info['user_group'];

				if (is_file(DIR_IMAGE . $user_info['image'])) {
					$data['image'] = $this->model_tool_image->resize($user_info['image'], 45, 45);
				} else {
					$data['image'] = $this->model_tool_image->resize('profile.png', 45, 45);
				}
			} else {
				$data['firstname'] = '';
				$data['lastname'] = '';
				$data['user_group'] = '';
				$data['image'] = '';
			}

			// Online Stores
			$data['stores'] = array();

			$data['stores'][] = array(
				'name' => $this->config->get('config_name'),
				'href' => HTTP_CATALOG
			);

			$this->load->model('setting/store');

			$results = $this->model_setting_store->getStores();

			foreach ($results as $result) {
				$data['stores'][] = array(
					'name' => $result['name'],
					'href' => $result['url']
				);
			}
		}

		return $this->load->view('common/header', $data);
	}

	/**
	 * الحصول على بيانات الإشعارات الموحدة للهيدر
	 */
	public function getUnifiedNotifications() {
		$this->load->language('common/header');

		$json = [];

		if (!$this->user->hasPermission('access', 'common/header')) {
			$json['error'] = $this->language->get('error_permission');
		} else {
			try {
				$user_id = $this->user->getId();

				// تحميل الخدمات المركزية
				$this->load->model('core/central_service_manager');
				$central_service = $this->model_core_central_service_manager;

				// الحصول على الإشعارات العامة
				$notifications = $this->getSystemNotifications($user_id);

				// الحصول على الرسائل غير المقروءة
				$messages = $this->getUnreadMessages($user_id);

				// الحصول على طلبات الموافقة المعلقة
				$approvals = $this->getPendingApprovals($user_id);

				// الحصول على تنبيهات المخزون
				$inventory_alerts = $this->getInventoryAlerts($user_id);

				// الحصول على تنبيهات انتهاء الصلاحية
				$expiry_alerts = $this->getExpiryAlerts($user_id);

				// الحصول على تنبيهات المهام
				$task_alerts = $this->getTaskAlerts($user_id);

				// تجميع جميع الإشعارات
				$all_notifications = array_merge(
					$notifications,
					$messages,
					$approvals,
					$inventory_alerts,
					$expiry_alerts,
					$task_alerts
				);

				// ترتيب حسب الأولوية والوقت
				usort($all_notifications, function($a, $b) {
					$priority_order = ['critical' => 5, 'urgent' => 4, 'high' => 3, 'normal' => 2, 'low' => 1];
					$a_priority = $priority_order[$a['priority']] ?? 2;
					$b_priority = $priority_order[$b['priority']] ?? 2;

					if ($a_priority == $b_priority) {
						return strtotime($b['created_at']) - strtotime($a['created_at']);
					}
					return $b_priority - $a_priority;
				});

				// تحديد العدد الإجمالي
				$total_count = count($all_notifications);

				// أخذ أول 20 إشعار للعرض
				$display_notifications = array_slice($all_notifications, 0, 20);

				// تجميع الإحصائيات
				$stats = $this->getNotificationStats($all_notifications);

				$json['success'] = true;
				$json['data'] = [
					'total_count' => $total_count,
					'notifications' => $display_notifications,
					'stats' => $stats,
					'has_critical' => $stats['critical'] > 0,
					'has_urgent' => $stats['urgent'] > 0
				];

			} catch (Exception $e) {
				$json['error'] = $this->language->get('error_loading_notifications') . $e->getMessage();
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * الحصول على الإشعارات النظامية
	 */
	private function getSystemNotifications($user_id) {
		// استخدام مدير الخدمات المركزية
		if (!isset($this->model_core_central_service_manager)) {
			$this->load->model('core/central_service_manager');
		}

		try {
			$notifications_service = $this->model_core_central_service_manager->getService('notifications');
			$notifications = $notifications_service->getUnreadNotifications($user_id, 10);
		} catch (Exception $e) {
			// Fallback to direct model loading
			$this->load->model('communication/unified_notification');
			$notifications = $this->model_communication_unified_notification->getUnreadNotifications($user_id, 10);
		}

		$result = [];
		foreach ($notifications as $notification) {
			$result[] = [
				'id' => $notification['notification_id'],
				'type' => 'notification',
				'category' => $notification['type'] ?? 'info',
				'priority' => $notification['priority'] ?? 'normal',
				'title' => $notification['title'],
				'message' => $notification['content'],
				'icon' => $this->getNotificationIcon($notification['type'] ?? 'info'),
				'color' => $this->getNotificationColor($notification['priority'] ?? 'normal'),
				'action_url' => $notification['action_url'] ?? '',
				'created_at' => $notification['created_at'],
				'time_ago' => $this->timeAgo($notification['created_at'])
			];
		}

		return $result;
	}

	/**
	 * الحصول على الرسائل غير المقروءة
	 */
	private function getUnreadMessages($user_id) {
		$this->load->model('communication/messages');

		$messages = $this->model_communication_messages->getMessages($user_id, 0, 5);

		$result = [];
		foreach ($messages as $message) {
			if ($message['read_status'] == 'unread') {
				$result[] = [
					'id' => $message['message_id'],
					'type' => 'message',
					'category' => 'message',
					'priority' => $message['priority'] ?? 'normal',
					'title' => $this->language->get('text_message_from') . $message['sender_name'],
					'message' => substr($message['message'], 0, 100) . '...',
					'icon' => 'fa-envelope',
					'color' => 'info',
					'action_url' => 'index.php?route=communication/messages&message_id=' . $message['message_id'],
					'created_at' => $message['created_at'],
					'time_ago' => $this->timeAgo($message['created_at'])
				];
			}
		}

		return $result;
	}

	/**
	 * الحصول على طلبات الموافقة المعلقة
	 */
	private function getPendingApprovals($user_id) {
		$this->load->model('communication/teams');

		$approvals = $this->model_communication_teams->getPendingApprovals($user_id);

		$result = [];
		foreach ($approvals as $approval) {
			$result[] = [
				'id' => $approval['request_id'],
				'type' => 'approval',
				'category' => 'approval',
				'priority' => $approval['priority'] ?? 'high',
				'title' => $this->language->get('text_approval_request') . $approval['title'],
				'message' => $this->language->get('text_from') . $approval['requester_name'],
				'icon' => 'fa-check-circle',
				'color' => 'warning',
				'action_url' => 'index.php?route=workflow/approval&request_id=' . $approval['request_id'],
				'created_at' => $approval['created_at'],
				'time_ago' => $this->timeAgo($approval['created_at'])
			];
		}

		return $result;
	}

	/**
	 * الحصول على تنبيهات المخزون
	 */
	private function getInventoryAlerts($user_id) {
		// التحقق من صلاحية الوصول للمخزون
		if (!$this->user->hasPermission('access', 'inventory/product')) {
			return [];
		}

		$this->load->model('inventory/product');

		// الحصول على المنتجات ذات المخزون المنخفض
		$low_stock_products = $this->model_inventory_product->getLowStockProducts(10);

		$result = [];
		foreach ($low_stock_products as $product) {
			$result[] = [
				'id' => 'stock_' . $product['product_id'],
				'type' => 'inventory',
				'category' => 'stock_alert',
				'priority' => $product['quantity'] <= 0 ? 'critical' : 'high',
				'title' => $this->language->get('text_low_stock') . $product['name'],
				'message' => $this->language->get('text_current_stock') . $product['quantity'] . $this->language->get('text_minimum_limit') . $product['minimum'],
				'icon' => 'fa-exclamation-triangle',
				'color' => $product['quantity'] <= 0 ? 'danger' : 'warning',
				'action_url' => 'index.php?route=inventory/product&product_id=' . $product['product_id'],
				'created_at' => date('Y-m-d H:i:s'),
				'time_ago' => $this->language->get('text_now')
			];
		}

		return $result;
	}

	/**
	 * الحصول على تنبيهات انتهاء الصلاحية
	 */
	private function getExpiryAlerts($user_id) {
		// التحقق من صلاحية الوصول للمخزون
		if (!$this->user->hasPermission('access', 'inventory/product')) {
			return [];
		}

		$this->load->model('inventory/product');

		// الحصول على المنتجات قاربة انتهاء الصلاحية (خلال 30 يوم)
		$expiring_products = $this->model_inventory_product->getExpiringProducts(30, 10);

		$result = [];
		foreach ($expiring_products as $product) {
			$days_left = ceil((strtotime($product['expiry_date']) - time()) / (60 * 60 * 24));

			$result[] = [
				'id' => 'expiry_' . $product['product_id'],
				'type' => 'expiry',
				'category' => 'expiry_alert',
				'priority' => $days_left <= 7 ? 'critical' : ($days_left <= 15 ? 'high' : 'normal'),
				'title' => $this->language->get('text_expiry_alert') . $product['name'],
				'message' => $this->language->get('text_expires_in') . $days_left . $this->language->get('text_days'),
				'icon' => 'fa-calendar-times',
				'color' => $days_left <= 7 ? 'danger' : ($days_left <= 15 ? 'warning' : 'info'),
				'action_url' => 'index.php?route=inventory/product&product_id=' . $product['product_id'],
				'created_at' => date('Y-m-d H:i:s'),
				'time_ago' => $this->language->get('text_now')
			];
		}

		return $result;
	}

	/**
	 * الحصول على تنبيهات المهام
	 */
	private function getTaskAlerts($user_id) {
		// التحقق من صلاحية الوصول للمهام
		if (!$this->user->hasPermission('access', 'workflow/task')) {
			return [];
		}

		$this->load->model('workflow/task');

		// الحصول على المهام المتأخرة والمستحقة قريباً
		$overdue_tasks = $this->model_workflow_task->getOverdueTasks($user_id, 5);
		$upcoming_tasks = $this->model_workflow_task->getUpcomingTasks($user_id, 5);

		$result = [];

		// المهام المتأخرة
		foreach ($overdue_tasks as $task) {
			$result[] = [
				'id' => 'task_overdue_' . $task['task_id'],
				'type' => 'task',
				'category' => 'task_overdue',
				'priority' => 'critical',
				'title' => $this->language->get('text_overdue_task') . $task['title'],
				'message' => $this->language->get('text_overdue_by') . $this->timeAgo($task['due_date']),
				'icon' => 'fa-clock',
				'color' => 'danger',
				'action_url' => 'index.php?route=workflow/task&task_id=' . $task['task_id'],
				'created_at' => $task['due_date'],
				'time_ago' => $this->timeAgo($task['due_date'])
			];
		}

		// المهام المستحقة قريباً
		foreach ($upcoming_tasks as $task) {
			$result[] = [
				'id' => 'task_upcoming_' . $task['task_id'],
				'type' => 'task',
				'category' => 'task_upcoming',
				'priority' => 'high',
				'title' => $this->language->get('text_upcoming_task') . $task['title'],
				'message' => $this->language->get('text_due') . $this->timeAgo($task['due_date']),
				'icon' => 'fa-tasks',
				'color' => 'warning',
				'action_url' => 'index.php?route=workflow/task&task_id=' . $task['task_id'],
				'created_at' => $task['due_date'],
				'time_ago' => $this->timeAgo($task['due_date'])
			];
		}

		return $result;
	}

	/**
	 * الحصول على إحصائيات الإشعارات
	 */
	private function getNotificationStats($notifications) {
		$stats = [
			'total' => count($notifications),
			'critical' => 0,
			'urgent' => 0,
			'high' => 0,
			'normal' => 0,
			'low' => 0,
			'by_type' => []
		];

		foreach ($notifications as $notification) {
			// إحصائيات الأولوية
			$priority = $notification['priority'] ?? 'normal';
			if (isset($stats[$priority])) {
				$stats[$priority]++;
			}

			// إحصائيات النوع
			$type = $notification['type'] ?? 'other';
			if (!isset($stats['by_type'][$type])) {
				$stats['by_type'][$type] = 0;
			}
			$stats['by_type'][$type]++;
		}

		return $stats;
	}

	/**
	 * الحصول على أيقونة الإشعار حسب النوع
	 */
	private function getNotificationIcon($type) {
		$icons = [
			'info' => 'fa-info-circle',
			'success' => 'fa-check-circle',
			'warning' => 'fa-exclamation-triangle',
			'error' => 'fa-times-circle',
			'approval' => 'fa-check-circle',
			'reminder' => 'fa-bell',
			'system' => 'fa-cog',
			'message' => 'fa-envelope',
			'task' => 'fa-tasks',
			'inventory' => 'fa-cubes',
			'expiry' => 'fa-calendar-times'
		];

		return $icons[$type] ?? 'fa-bell';
	}

	/**
	 * الحصول على لون الإشعار حسب الأولوية
	 */
	private function getNotificationColor($priority) {
		$colors = [
			'critical' => 'danger',
			'urgent' => 'danger',
			'high' => 'warning',
			'normal' => 'info',
			'low' => 'muted'
		];

		return $colors[$priority] ?? 'info';
	}

	/**
	 * الحصول على بيانات الهيدر الشاملة
	 */
	public function getHeaderData() {
		$json = array();

		if ($this->user->isLogged()) {
			try {
				// تحميل مدير الخدمات المركزية
				$this->load->model('core/central_service_manager');

				// تحميل النماذج المطلوبة كـ fallback
				$this->load->model('communication/unified_notification');
				$this->load->model('communication/message');
				$this->load->model('workflow/approval');

				// الحصول على بيانات الإشعارات
				$notifications = $this->model_communication_unified_notification->getUserNotifications($this->user->getId(), 50);
				$notifications_data = array(
					'items' => $notifications,
					'unread_count' => $this->model_communication_unified_notification->getUnreadCount($this->user->getId()),
					'stats' => $this->getNotificationStats($notifications),
					'critical_count' => $this->model_communication_unified_notification->getCriticalCount($this->user->getId())
				);

				// الحصول على بيانات الرسائل
				$messages = $this->model_communication_message->getUserMessages($this->user->getId(), 20);
				$messages_data = array(
					'items' => $messages,
					'unread_count' => $this->model_communication_message->getUnreadCount($this->user->getId())
				);

				// الحصول على بيانات الموافقات
				$approvals = $this->model_workflow_approval->getPendingApprovals($this->user->getId());
				$approvals_data = array(
					'items' => $approvals,
					'pending_count' => count($approvals)
				);

				$data = array(
					'notifications' => $notifications_data,
					'messages' => $messages_data,
					'approvals' => $approvals_data,
					'indicators' => $this->getSystemIndicators(),
					'system_status' => $this->getSystemStatus(),
					'new_notifications' => $notifications_data['unread_count'] ?? 0,
					'critical_notifications' => $notifications_data['critical_count'] ?? 0
				);

				$json['success'] = true;
				$json['data'] = $data;

			} catch (Exception $e) {
				$json['error'] = 'خطأ في تحميل بيانات الهيدر: ' . $e->getMessage();
			}
		} else {
			$json['error'] = 'المستخدم غير مسجل الدخول';
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * حساب الوقت المنقضي
	 */
	private function timeAgo($datetime) {
		$time = time() - strtotime($datetime);

		if ($time < 60) {
			return $this->language->get('text_moments_ago');
		} elseif ($time < 3600) {
			$minutes = floor($time / 60);
			return "{$minutes} " . $this->language->get('text_minutes_ago');
		} elseif ($time < 86400) {
			$hours = floor($time / 3600);
			return "{$hours} " . $this->language->get('text_hours_ago');
		} elseif ($time < 2592000) {
			$days = floor($time / 86400);
			return "{$days} " . $this->language->get('text_days_ago');
		} else {
			return date('Y-m-d', strtotime($datetime));
		}
	}

	/**
	 * تحديد إشعار كمقروء
	 */
	public function markAsRead() {
		$this->load->language('common/header');

		$json = [];

		if (!$this->user->hasPermission('access', 'common/header')) {
			$json['error'] = $this->language->get('error_permission');
		} else {
			$notification_id = $this->request->post['notification_id'] ?? '';
			$notification_type = $this->request->post['notification_type'] ?? '';

			if ($notification_id && $notification_type) {
				try {
					switch ($notification_type) {
						case 'notification':
							$this->load->model('communication/unified_notification');
							$this->model_communication_unified_notification->markAsRead($notification_id, $this->user->getId());
							break;
						case 'message':
							$this->load->model('communication/messages');
							$this->model_communication_messages->markAsRead($notification_id, $this->user->getId());
							break;
						// يمكن إضافة أنواع أخرى حسب الحاجة
					}

					$json['success'] = true;
				} catch (Exception $e) {
					$json['error'] = $this->language->get('error_updating_notification') . $e->getMessage();
				}
			} else {
				$json['error'] = $this->language->get('error_invalid_data');
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * تحديد جميع الإشعارات كمقروءة
	 */
	public function markAllAsRead() {
		$this->load->language('common/header');

		$json = [];

		if (!$this->user->hasPermission('access', 'common/header')) {
			$json['error'] = $this->language->get('error_permission');
		} else {
			try {
				$user_id = $this->user->getId();

				// تحديد جميع الإشعارات كمقروءة
				$this->load->model('communication/unified_notification');
				$this->model_communication_unified_notification->markAllAsRead($user_id);

				// تحديد جميع الرسائل كمقروءة
				$this->load->model('communication/messages');
				$this->model_communication_messages->markAllAsRead($user_id);

				$json['success'] = true;
				$json['message'] = $this->language->get('text_all_notifications_marked_read');

			} catch (Exception $e) {
				$json['error'] = $this->language->get('error_updating_notifications') . $e->getMessage();
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * مسح الإشعارات المقروءة
	 */
	public function clearRead() {
		$json = array();

		if ($this->user->isLogged()) {
			try {
				$this->load->model('communication/unified_notification');

				// مسح الإشعارات المقروءة للمستخدم الحالي
				$this->model_communication_unified_notification->clearReadNotifications($this->user->getId());

				$json['success'] = true;
				$json['message'] = 'تم مسح الإشعارات المقروءة بنجاح';

			} catch (Exception $e) {
				$json['error'] = 'خطأ في مسح الإشعارات: ' . $e->getMessage();
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * الحصول على بيانات التبويب المحدد
	 */
	public function getTabData() {
		$json = array();

		if ($this->user->isLogged() && isset($this->request->get['type'])) {
			$type = $this->request->get['type'];

			try {
				$this->load->model('communication/unified_notification');

				switch ($type) {
					case 'critical':
						$data = $this->model_communication_unified_notification->getCriticalNotifications($this->user->getId());
						break;
					case 'approvals':
						$data = $this->getApprovalRequests();
						break;
					case 'workflow':
						$data = $this->getWorkflowNotifications();
						break;
					case 'documents':
						$data = $this->getDocumentNotifications();
						break;
					case 'security':
						$data = $this->getSecurityNotifications();
						break;
					default:
						$data = array();
				}

				$json['success'] = true;
				$json['data'] = $data;

			} catch (Exception $e) {
				$json['error'] = 'خطأ في تحميل البيانات: ' . $e->getMessage();
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * الحصول على طلبات الموافقة
	 */
	private function getApprovalRequests() {
		$this->load->model('workflow/approval');
		return $this->model_workflow_approval->getPendingApprovals($this->user->getId());
	}

	/**
	 * الحصول على إشعارات سير العمل
	 */
	private function getWorkflowNotifications() {
		$this->load->model('workflow/workflow');
		return $this->model_workflow_workflow->getUserWorkflowNotifications($this->user->getId());
	}

	/**
	 * الحصول على إشعارات المستندات
	 */
	private function getDocumentNotifications() {
		try {
			// استخدام مدير الخدمات المركزية
			if (isset($this->model_core_central_service_manager)) {
				$documents_service = $this->model_core_central_service_manager->getService('documents');
				return $documents_service->getUserDocumentNotifications($this->user->getId());
			}
		} catch (Exception $e) {
			// Fallback to direct model loading
		}

		// Fallback - تحميل النموذج مباشرة
		$this->load->model('unified_document');
		return $this->model_unified_document->getUserDocumentNotifications($this->user->getId());
	}

	/**
	 * الحصول على التنبيهات الأمنية
	 */
	private function getSecurityNotifications() {
		$this->load->model('security/security_log');
		return $this->model_security_security_log->getUserSecurityNotifications($this->user->getId());
	}

	/**
	 * الحصول على مؤشرات النظام
	 */
	private function getSystemIndicators() {
		$indicators = array();

		try {
			// مؤشر الأداء
			$indicators['performance'] = $this->getSystemPerformance();

			// المستخدمين النشطين
			$indicators['active_users'] = $this->getActiveUsersCount();

			// مبيعات اليوم
			$indicators['today_sales'] = $this->getTodaySales();

			// المهام المعلقة
			$indicators['pending_tasks'] = $this->getPendingTasksCount();

		} catch (Exception $e) {
			// في حالة الخطأ، إرجاع قيم افتراضية
			$indicators = array(
				'performance' => 95,
				'active_users' => 1,
				'today_sales' => '0',
				'pending_tasks' => 0
			);
		}

		return $indicators;
	}

	/**
	 * الحصول على أداء النظام
	 */
	private function getSystemPerformance() {
		// حساب أداء النظام بناءً على عوامل مختلفة
		$performance = 95; // قيمة افتراضية

		// يمكن إضافة حسابات أكثر تعقيداً هنا
		// مثل استخدام الذاكرة، سرعة قاعدة البيانات، إلخ

		return $performance;
	}

	/**
	 * الحصول على عدد المستخدمين النشطين
	 */
	private function getActiveUsersCount() {
		$sql = "SELECT COUNT(DISTINCT user_id) as count
				FROM " . DB_PREFIX . "user_session
				WHERE last_activity > DATE_SUB(NOW(), INTERVAL 30 MINUTE)";

		$query = $this->db->query($sql);

		return $query->row['count'] ?? 1;
	}

	/**
	 * الحصول على مبيعات اليوم
	 */
	private function getTodaySales() {
		$sql = "SELECT COALESCE(SUM(total), 0) as total
				FROM " . DB_PREFIX . "order
				WHERE DATE(date_added) = CURDATE()
				AND order_status_id > 0";

		$query = $this->db->query($sql);
		$total = $query->row['total'] ?? 0;

		// تنسيق الرقم
		if ($total >= 1000000) {
			return number_format($total / 1000000, 1) . 'M';
		} elseif ($total >= 1000) {
			return number_format($total / 1000, 1) . 'K';
		}

		return number_format($total, 0);
	}

	/**
	 * الحصول على عدد المهام المعلقة
	 */
	private function getPendingTasksCount() {
		$sql = "SELECT COUNT(*) as count
				FROM " . DB_PREFIX . "workflow_task
				WHERE assigned_to = '" . (int)$this->user->getId() . "'
				AND status = 'pending'";

		$query = $this->db->query($sql);

		return $query->row['count'] ?? 0;
	}

	/**
	 * الحصول على حالة النظام
	 */
	private function getSystemStatus() {
		$status = array(
			'level' => 'healthy',
			'message' => 'النظام يعمل بكفاءة'
		);

		// فحص حالة قاعدة البيانات
		try {
			$this->db->query("SELECT 1");
		} catch (Exception $e) {
			$status['level'] = 'critical';
			$status['message'] = 'مشكلة في قاعدة البيانات';
			return $status;
		}

		// فحص مساحة القرص الصلب (إذا كان متاحاً)
		if (function_exists('disk_free_space')) {
			$free_space = disk_free_space('/');
			$total_space = disk_total_space('/');

			if ($free_space && $total_space) {
				$usage_percent = (($total_space - $free_space) / $total_space) * 100;

				if ($usage_percent > 90) {
					$status['level'] = 'warning';
					$status['message'] = 'مساحة القرص منخفضة';
				}
			}
		}

		return $status;
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// 🚀 KPIs API Endpoints - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Get KPIs Data for Header Widgets
	 * جلب بيانات مؤشرات الأداء لويدجت الهيدر
	 */
	public function getKPIs() {
		$this->load->model('common/dashboard');

		// التحقق من الصلاحيات
		if (!$this->user->hasPermission('access', 'common/dashboard')) {
			$json = ['error' => true, 'message' => 'Access denied'];
		} else {
			try {
				// جلب الفئة المطلوبة
				$category = isset($this->request->get['category']) ? $this->request->get['category'] : 'all';

				// جلب مؤشرات الأداء
				$kpis = $this->model_common_dashboard->getAllKPIs($category);

				$json = [
					'success' => true,
					'data' => $kpis,
					'timestamp' => time()
				];

			} catch (Exception $e) {
				$json = [
					'error' => true,
					'message' => 'فشل في جلب مؤشرات الأداء: ' . $e->getMessage()
				];
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * Get KPI Summary for Quick View
	 * ملخص مؤشرات الأداء للعرض السريع
	 */
	public function getKPISummary() {
		$this->load->model('common/dashboard');

		if (!$this->user->hasPermission('access', 'common/dashboard')) {
			$json = ['error' => true, 'message' => 'Access denied'];
		} else {
			try {
				$summary = $this->model_common_dashboard->getKPISummary();

				$json = [
					'success' => true,
					'data' => $summary,
					'timestamp' => time()
				];

			} catch (Exception $e) {
				$json = [
					'error' => true,
					'message' => 'فشل في جلب ملخص مؤشرات الأداء: ' . $e->getMessage()
				];
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * Get KPIs Count
	 * عدد مؤشرات الأداء المتاحة
	 */
	public function getKPIsCount() {
		$this->load->model('common/dashboard');

		if (!$this->user->hasPermission('access', 'common/dashboard')) {
			$json = ['error' => true, 'message' => 'Access denied'];
		} else {
			try {
				$count = $this->model_common_dashboard->getKPIsCount();

				$json = [
					'success' => true,
					'data' => ['total_kpis' => $count],
					'timestamp' => time()
				];

			} catch (Exception $e) {
				$json = [
					'error' => true,
					'message' => 'فشل في عد مؤشرات الأداء: ' . $e->getMessage()
				];
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

}