<?php
/**
 * كونترولر تسجيل الدخول مع المصادقة الثنائية - AYM ERP
 *
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0 (مع دعم 2FA)
 */

class ControllerCommonLogin extends Controller {
	private $error = array();

	public function index() {
		// Load central services (CONSTITUTIONAL REQUIREMENT)
		$this->load->model('core/central_service_manager');

		$this->load->language('common/login');

		// تحميل نموذج المصادقة الثنائية مع معالجة الأخطاء
		$two_factor_available = false;
		try {
			$this->load->model('user/two_factor_auth');
			$two_factor_available = true;
		} catch (Exception $e) {
			// النموذج غير موجود، متابعة بدون مصادقة ثنائية
			error_log('Two Factor Auth model not found: ' . $e->getMessage());
		}

		$this->document->setTitle($this->language->get('heading_title'));

		// إذا كان المستخدم مسجل دخول بالفعل
		if ($this->user->isLogged()) {
			// التأكد من وجود التوكن
			if (!isset($this->session->data['user_token'])) {
				$this->session->data['user_token'] = token(32);
			}

			// إعادة التوجيه للوحة الرئيسية
			$this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
		}
      

		// معالجة تسجيل الدخول
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
			// تسجيل debug مؤقت
			error_log('AYM DEBUG: Login validation passed, user ID: ' . $this->user->getId());
			// التحقق من المصادقة الثنائية إذا كان النموذج متاحاً
			if ($two_factor_available) {
				try {
					$user_info = $this->model_user_two_factor_auth->getUserTwoFactorInfo($this->user->getId());

					if ($user_info && $user_info['two_factor_enabled']) {
						// التحقق من الجهاز الموثوق
						$device_fingerprint = $this->generateDeviceFingerprint();
						$is_trusted = $this->model_user_two_factor_auth->isTrustedDevice($this->user->getId(), $device_fingerprint);

						if (!$is_trusted) {
							// حفظ معلومات المستخدم في الجلسة للمصادقة الثنائية
							$this->session->data['pending_2fa_user_id'] = $this->user->getId();
							$this->session->data['pending_2fa_redirect'] = isset($this->request->post['redirect']) ? $this->request->post['redirect'] : '';

							// إعادة توجيه لصفحة المصادقة الثنائية
							$this->response->redirect($this->url->link('common/two_factor_verify', '', true));
						} else {
							// الجهاز موثوق، متابعة تسجيل الدخول
							$this->completeLogin();
						}
					} else {
						// لا توجد مصادقة ثنائية، متابعة تسجيل الدخول العادي
						$this->completeLogin();
					}
				} catch (Exception $e) {
					// خطأ في المصادقة الثنائية، متابعة تسجيل الدخول العادي
					error_log('Two Factor Auth error: ' . $e->getMessage());
					$this->completeLogin();
				}
			} else {
				// المصادقة الثنائية غير متاحة، متابعة تسجيل الدخول العادي
				$this->completeLogin();
			}
		}

		// إزالة فحص التوكن من صفحة تسجيل الدخول لتجنب الحلقة المفرغة
		// التوكن سيتم إنشاؤه بعد تسجيل الدخول الناجح

		if (isset($this->error['error_attempts'])) {
			$data['error_warning'] = $this->error['error_attempts'];
		} elseif (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}
      
		if ($this->request->server['HTTPS']) {
			$data['base'] = HTTPS_SERVER;
		} else {
			$data['base'] = HTTP_SERVER;
		}
		$data['action'] = $this->url->link('common/login', '', true);

		if (isset($this->request->post['username'])) {
			$data['username'] = $this->request->post['username'];
		} else {
			$data['username'] = '';
		}

		if (isset($this->request->post['password'])) {
			$data['password'] = $this->request->post['password'];
		} else {
			$data['password'] = '';
		}

		if (isset($this->request->get['route'])) {
			$route = $this->request->get['route'];

			unset($this->request->get['route']);
			unset($this->request->get['user_token']);

			$url = '';

			if ($this->request->get) {
				$url .= http_build_query($this->request->get);
			}

			$data['redirect'] = $this->url->link($route, $url, true);
		} else {
			$data['redirect'] = '';
		}

		if ($this->config->get('config_password')) {
			$data['forgotten'] = $this->url->link('common/forgotten', '', true);
		} else {
			$data['forgotten'] = '';
		}

		// Load all language entries for the login page
		$data['text_login'] = $this->language->get('text_login');
		$data['text_login_subtitle'] = $this->language->get('text_login_subtitle');
		$data['text_welcome'] = $this->language->get('text_welcome');
		$data['text_secure_login'] = $this->language->get('text_secure_login');
		$data['text_remember_me'] = $this->language->get('text_remember_me');
		$data['text_forgotten'] = $this->language->get('text_forgotten');
		$data['text_secure'] = $this->language->get('text_secure');
		$data['text_secure_connection'] = $this->language->get('text_secure_connection');
		$data['text_2fa_support'] = $this->language->get('text_2fa_support');
		$data['text_session_timeout'] = $this->language->get('text_session_timeout');
		$data['text_logging_in'] = $this->language->get('text_logging_in');
		$data['text_authenticating'] = $this->language->get('text_authenticating');
		$data['text_version'] = $this->language->get('text_version');
		$data['text_powered_by'] = $this->language->get('text_powered_by');

		// Input fields
		$data['entry_username'] = $this->language->get('entry_username');
		$data['entry_password'] = $this->language->get('entry_password');

		// Buttons
		$data['button_login'] = $this->language->get('button_login');

		// Error messages for JavaScript
		$data['error_username'] = $this->language->get('error_username');
		$data['error_password'] = $this->language->get('error_password');

		$data['header'] = $this->load->controller('common/header');
		$data['footer'] = $this->load->controller('common/footer');

		// Check if the template file exists
		$template_file = DIR_TEMPLATE . 'common/login.twig';
		if (!is_file($template_file)) {
			$this->log->write('Error: Login template file missing: ' . $template_file);
		}

		$this->response->setOutput($this->load->view('common/login', $data));
	}



	protected function validate() {
		if (!isset($this->request->post['username']) || !isset($this->request->post['password']) || !$this->request->post['username'] || !$this->request->post['password']) {
			$this->error['warning'] = $this->language->get('error_login');
		} else {
			$this->load->model('user/user');

			// Check how many login attempts have been made.
			$login_info = $this->model_user_user->getLoginAttempts($this->request->post['username']);

			if ($login_info && ($login_info['total'] >= $this->config->get('config_login_attempts')) && strtotime('-1 hour') < strtotime($login_info['date_modified'])) {
				$this->error['error_attempts'] = $this->language->get('error_attempts');
			}
		}

		if (!$this->error) {
			if (!$this->user->login($this->request->post['username'], html_entity_decode($this->request->post['password'], ENT_QUOTES, 'UTF-8'))) {
				$this->error['warning'] = $this->language->get('error_login');

				$this->model_user_user->addLoginAttempt($this->request->post['username']);

				unset($this->session->data['user_token']);
			} else {
				$this->model_user_user->deleteLoginAttempts($this->request->post['username']);
			}
		}

		return !$this->error;
	}

	/**
	 * إكمال عملية تسجيل الدخول
	 */
	private function completeLogin() {
		$this->session->data['user_token'] = token(32);

		// تسجيل debug مؤقت
		error_log('AYM DEBUG: completeLogin called, token: ' . $this->session->data['user_token']);

		// معالجة خيار "تذكرني"
		if (isset($this->request->post['remember']) && $this->request->post['remember'] == '1') {
			$this->session->data['remember_me'] = true;

			// تعيين كوكي طويل المدى للاحتفاظ بالجلسة
			$remember_time = time() + (7 * 24 * 60 * 60); // أسبوع
			setcookie('aym_remember_token', $this->session->data['user_token'], $remember_time, '/', '', false, true);
			setcookie('aym_remember_user', $this->user->getId(), $remember_time, '/', '', false, true);
		}

		// تسجيل نشاط تسجيل الدخول باستخدام الخدمات المركزية
		$this->load->model('core/central_service_manager');

		$this->model_core_central_service_manager->logLogin(
			$this->user->getId(),
			true,
			'تسجيل دخول ناجح من ' . ($this->request->server['REMOTE_ADDR'] ?? 'غير معروف')
		);

		// إعادة التوجيه
		if (isset($this->request->post['redirect']) && !empty($this->request->post['redirect']) &&
			(strpos($this->request->post['redirect'], HTTP_SERVER) === 0 || strpos($this->request->post['redirect'], HTTPS_SERVER) === 0)) {

			// إضافة التوكن للرابط
			$redirect_url = $this->request->post['redirect'];
			$separator = strpos($redirect_url, '?') !== false ? '&' : '?';
			$redirect_url .= $separator . 'user_token=' . $this->session->data['user_token'];

			$this->response->redirect($redirect_url);
		} else {
			$this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
		}
	}

	/**
	 * توليد بصمة الجهاز
	 */
	private function generateDeviceFingerprint() {
		$components = [
			$this->request->server['HTTP_USER_AGENT'] ?? '',
			$this->request->server['HTTP_ACCEPT_LANGUAGE'] ?? '',
			$this->request->server['HTTP_ACCEPT_ENCODING'] ?? '',
			$this->request->server['REMOTE_ADDR'] ?? ''
		];

		return hash('sha256', implode('|', $components));
	}

	/**
	 * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
	 * Sanitize all output data to prevent XSS attacks
	 */
	private function sanitizeOutputData($data) {
		if (is_array($data)) {
			foreach ($data as $key => $value) {
				$data[$key] = $this->sanitizeOutputData($value);
			}
		} elseif (is_string($data)) {
			$data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
		}
		return $data;
	}
}
