<?php
/**
 * ملف اللغة العربية - إدارة سير العمل
 * Arabic Language File - Workflow Management
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// العنوان الرئيسي
$_['heading_title'] = 'إدارة سير العمل';

// النصوص العامة
$_['text_success'] = 'تم: تم تعديل سير العمل بنجاح!';
$_['text_list'] = 'قائمة سير العمل';
$_['text_add'] = 'إضافة سير عمل';
$_['text_edit'] = 'تعديل سير العمل';
$_['text_view'] = 'عرض سير العمل';
$_['text_delete'] = 'حذف';
$_['text_confirm'] = 'هل أنت متأكد؟';
$_['text_no_results'] = 'لا توجد نتائج!';
$_['text_loading'] = 'جاري التحميل...';

// حالات سير العمل
$_['text_status_draft'] = 'مسودة';
$_['text_status_active'] = 'نشط';
$_['text_status_inactive'] = 'غير نشط';
$_['text_status_paused'] = 'متوقف';
$_['text_status_completed'] = 'مكتمل';
$_['text_status_cancelled'] = 'ملغي';
$_['text_status_archived'] = 'مؤرشف';
$_['text_status_testing'] = 'قيد الاختبار';

// أنواع سير العمل
$_['text_type_approval'] = 'موافقة';
$_['text_type_review'] = 'مراجعة';
$_['text_type_notification'] = 'إشعار';
$_['text_type_automation'] = 'أتمتة';
$_['text_type_escalation'] = 'تصعيد';
$_['text_type_sequential'] = 'تسلسلي';
$_['text_type_parallel'] = 'متوازي';
$_['text_type_conditional'] = 'شرطي';

// أولويات سير العمل
$_['text_priority_low'] = 'منخفضة';
$_['text_priority_normal'] = 'عادية';
$_['text_priority_high'] = 'عالية';
$_['text_priority_urgent'] = 'عاجلة';
$_['text_priority_critical'] = 'حرجة';

// الحقول
$_['entry_name'] = 'اسم سير العمل';
$_['entry_description'] = 'الوصف';
$_['entry_type'] = 'نوع سير العمل';
$_['entry_category'] = 'الفئة';
$_['entry_status'] = 'الحالة';
$_['entry_priority'] = 'الأولوية';
$_['entry_department'] = 'القسم';
$_['entry_owner'] = 'المالك';
$_['entry_trigger'] = 'المحفز';
$_['entry_conditions'] = 'الشروط';
$_['entry_actions'] = 'الإجراءات';
$_['entry_timeout'] = 'انتهاء الوقت (دقائق)';
$_['entry_escalation'] = 'التصعيد';
$_['entry_notification'] = 'الإشعارات';

// الأعمدة
$_['column_name'] = 'الاسم';
$_['column_type'] = 'النوع';
$_['column_category'] = 'الفئة';
$_['column_status'] = 'الحالة';
$_['column_priority'] = 'الأولوية';
$_['column_owner'] = 'المالك';
$_['column_department'] = 'القسم';
$_['column_created'] = 'تاريخ الإنشاء';
$_['column_modified'] = 'تاريخ التعديل';
$_['column_last_run'] = 'آخر تشغيل';
$_['column_runs_count'] = 'عدد مرات التشغيل';
$_['column_success_rate'] = 'معدل النجاح';
$_['column_action'] = 'الإجراء';

// الأزرار
$_['button_add'] = 'إضافة سير عمل';
$_['button_edit'] = 'تعديل';
$_['button_delete'] = 'حذف';
$_['button_view'] = 'عرض';
$_['button_copy'] = 'نسخ';
$_['button_activate'] = 'تفعيل';
$_['button_deactivate'] = 'إلغاء التفعيل';
$_['button_pause'] = 'إيقاف';
$_['button_resume'] = 'استئناف';
$_['button_test'] = 'اختبار';
$_['button_run'] = 'تشغيل';
$_['button_export'] = 'تصدير';
$_['button_import'] = 'استيراد';
$_['button_logs'] = 'السجلات';
$_['button_statistics'] = 'الإحصائيات';

// إدارة الطلبات
$_['text_requests'] = 'طلبات سير العمل';
$_['text_active_requests'] = 'الطلبات النشطة';
$_['text_pending_requests'] = 'الطلبات المعلقة';
$_['text_completed_requests'] = 'الطلبات المكتملة';
$_['text_failed_requests'] = 'الطلبات الفاشلة';
$_['text_cancelled_requests'] = 'الطلبات الملغية';

// حالات الطلبات
$_['text_request_status_new'] = 'جديد';
$_['text_request_status_in_progress'] = 'قيد التنفيذ';
$_['text_request_status_waiting'] = 'في الانتظار';
$_['text_request_status_approved'] = 'معتمد';
$_['text_request_status_rejected'] = 'مرفوض';
$_['text_request_status_completed'] = 'مكتمل';
$_['text_request_status_failed'] = 'فاشل';
$_['text_request_status_cancelled'] = 'ملغي';
$_['text_request_status_escalated'] = 'مصعد';

// خطوات سير العمل
$_['text_steps'] = 'خطوات سير العمل';
$_['text_step_details'] = 'تفاصيل الخطوة';
$_['text_step_name'] = 'اسم الخطوة';
$_['text_step_type'] = 'نوع الخطوة';
$_['text_step_order'] = 'ترتيب الخطوة';
$_['text_step_conditions'] = 'شروط الخطوة';
$_['text_step_actions'] = 'إجراءات الخطوة';
$_['text_step_timeout'] = 'انتهاء وقت الخطوة';
$_['text_step_assignee'] = 'المكلف بالخطوة';

// أنواع الخطوات
$_['text_step_type_start'] = 'بداية';
$_['text_step_type_approval'] = 'موافقة';
$_['text_step_type_review'] = 'مراجعة';
$_['text_step_type_notification'] = 'إشعار';
$_['text_step_type_task'] = 'مهمة';
$_['text_step_type_condition'] = 'شرط';
$_['text_step_type_loop'] = 'حلقة تكرار';
$_['text_step_type_parallel'] = 'متوازي';
$_['text_step_type_merge'] = 'دمج';
$_['text_step_type_end'] = 'نهاية';

// المحفزات
$_['text_triggers'] = 'المحفزات';
$_['text_trigger_manual'] = 'يدوي';
$_['text_trigger_automatic'] = 'تلقائي';
$_['text_trigger_scheduled'] = 'مجدول';
$_['text_trigger_event'] = 'حدث';
$_['text_trigger_condition'] = 'شرط';
$_['text_trigger_webhook'] = 'Webhook';
$_['text_trigger_email'] = 'بريد إلكتروني';
$_['text_trigger_api'] = 'API';

// الشروط
$_['text_conditions'] = 'الشروط';
$_['text_condition_field'] = 'الحقل';
$_['text_condition_operator'] = 'المشغل';
$_['text_condition_value'] = 'القيمة';
$_['text_condition_logic'] = 'المنطق';
$_['text_condition_and'] = 'و (AND)';
$_['text_condition_or'] = 'أو (OR)';
$_['text_condition_not'] = 'ليس (NOT)';

// المشغلات
$_['text_operator_equals'] = 'يساوي';
$_['text_operator_not_equals'] = 'لا يساوي';
$_['text_operator_greater'] = 'أكبر من';
$_['text_operator_greater_equal'] = 'أكبر من أو يساوي';
$_['text_operator_less'] = 'أصغر من';
$_['text_operator_less_equal'] = 'أصغر من أو يساوي';
$_['text_operator_contains'] = 'يحتوي على';
$_['text_operator_not_contains'] = 'لا يحتوي على';
$_['text_operator_starts_with'] = 'يبدأ بـ';
$_['text_operator_ends_with'] = 'ينتهي بـ';
$_['text_operator_is_empty'] = 'فارغ';
$_['text_operator_is_not_empty'] = 'غير فارغ';

// الإجراءات
$_['text_actions'] = 'الإجراءات';
$_['text_action_send_email'] = 'إرسال بريد إلكتروني';
$_['text_action_send_sms'] = 'إرسال رسالة نصية';
$_['text_action_send_notification'] = 'إرسال إشعار';
$_['text_action_update_field'] = 'تحديث حقل';
$_['text_action_create_record'] = 'إنشاء سجل';
$_['text_action_delete_record'] = 'حذف سجل';
$_['text_action_call_webhook'] = 'استدعاء Webhook';
$_['text_action_run_script'] = 'تشغيل سكريبت';
$_['text_action_assign_task'] = 'تكليف مهمة';
$_['text_action_escalate'] = 'تصعيد';

// التصعيد
$_['text_escalation'] = 'التصعيد';
$_['text_escalation_enabled'] = 'تفعيل التصعيد';
$_['text_escalation_after'] = 'التصعيد بعد (دقائق)';
$_['text_escalation_to'] = 'التصعيد إلى';
$_['text_escalation_action'] = 'إجراء التصعيد';
$_['text_escalation_levels'] = 'مستويات التصعيد';

// الإشعارات
$_['text_notifications'] = 'الإشعارات';
$_['text_notification_on_start'] = 'إشعار عند البداية';
$_['text_notification_on_complete'] = 'إشعار عند الاكتمال';
$_['text_notification_on_error'] = 'إشعار عند الخطأ';
$_['text_notification_on_timeout'] = 'إشعار عند انتهاء الوقت';
$_['text_notification_recipients'] = 'مستقبلو الإشعارات';

// المتغيرات
$_['text_variables'] = 'المتغيرات';
$_['text_workflow_variables'] = 'متغيرات سير العمل';
$_['text_system_variables'] = 'متغيرات النظام';
$_['text_user_variables'] = 'متغيرات المستخدم';
$_['text_custom_variables'] = 'متغيرات مخصصة';

// السجلات والتقارير
$_['text_logs'] = 'السجلات';
$_['text_execution_logs'] = 'سجلات التنفيذ';
$_['text_error_logs'] = 'سجلات الأخطاء';
$_['text_audit_logs'] = 'سجلات التدقيق';
$_['text_performance_logs'] = 'سجلات الأداء';

// الإحصائيات
$_['text_statistics'] = 'الإحصائيات';
$_['text_execution_statistics'] = 'إحصائيات التنفيذ';
$_['text_performance_metrics'] = 'مقاييس الأداء';
$_['text_success_rate'] = 'معدل النجاح';
$_['text_failure_rate'] = 'معدل الفشل';
$_['text_average_duration'] = 'متوسط المدة';
$_['text_total_executions'] = 'إجمالي التنفيذات';
$_['text_active_instances'] = 'الحالات النشطة';

// البحث والتصفية
$_['text_search'] = 'بحث';
$_['text_search_workflows'] = 'بحث في سير العمل';
$_['text_filter'] = 'تصفية';
$_['text_filter_by_status'] = 'تصفية حسب الحالة';
$_['text_filter_by_type'] = 'تصفية حسب النوع';
$_['text_filter_by_category'] = 'تصفية حسب الفئة';
$_['text_filter_by_owner'] = 'تصفية حسب المالك';
$_['text_filter_by_department'] = 'تصفية حسب القسم';

// التصدير والاستيراد
$_['text_export'] = 'تصدير';
$_['text_import'] = 'استيراد';
$_['text_export_workflow'] = 'تصدير سير العمل';
$_['text_import_workflow'] = 'استيراد سير العمل';
$_['text_export_format'] = 'تنسيق التصدير';
$_['text_import_format'] = 'تنسيق الاستيراد';

// رسائل الخطأ
$_['error_permission'] = 'تحذير: ليس لديك صلاحية للوصول إلى إدارة سير العمل!';
$_['error_name'] = 'يجب أن يكون اسم سير العمل بين 3 و 255 حرف!';
$_['error_type'] = 'يجب اختيار نوع سير العمل!';
$_['error_category'] = 'يجب اختيار فئة سير العمل!';
$_['error_owner'] = 'يجب اختيار مالك سير العمل!';
$_['error_no_steps'] = 'يجب إضافة خطوة واحدة على الأقل!';
$_['error_invalid_step'] = 'خطوة غير صحيحة!';
$_['error_circular_reference'] = 'مرجع دائري غير مسموح!';
$_['error_workflow_running'] = 'لا يمكن تعديل سير العمل أثناء التشغيل!';
$_['error_execution_failed'] = 'فشل في تنفيذ سير العمل!';

// رسائل التأكيد
$_['text_confirm_delete'] = 'هل أنت متأكد من حذف سير العمل؟';
$_['text_confirm_activate'] = 'هل أنت متأكد من تفعيل سير العمل؟';
$_['text_confirm_deactivate'] = 'هل أنت متأكد من إلغاء تفعيل سير العمل؟';
$_['text_confirm_run'] = 'هل أنت متأكد من تشغيل سير العمل؟';

// المساعدة والنصائح
$_['help_name'] = 'أدخل اسم واضح ومميز لسير العمل';
$_['help_description'] = 'وصف مختصر لهدف ووظيفة سير العمل';
$_['help_type'] = 'اختر نوع سير العمل المناسب';
$_['help_trigger'] = 'حدد كيفية بدء تشغيل سير العمل';
$_['help_conditions'] = 'حدد الشروط اللازمة لتنفيذ سير العمل';

// التنبيهات
$_['alert_workflow_created'] = 'تم إنشاء سير العمل بنجاح';
$_['alert_workflow_updated'] = 'تم تحديث سير العمل بنجاح';
$_['alert_workflow_deleted'] = 'تم حذف سير العمل';
$_['alert_workflow_activated'] = 'تم تفعيل سير العمل';
$_['alert_workflow_deactivated'] = 'تم إلغاء تفعيل سير العمل';
$_['alert_workflow_executed'] = 'تم تنفيذ سير العمل بنجاح';

// التواريخ والأوقات
$_['text_created_at'] = 'تاريخ الإنشاء';
$_['text_updated_at'] = 'تاريخ التحديث';
$_['text_last_run_at'] = 'آخر تشغيل';
$_['text_next_run_at'] = 'التشغيل التالي';
$_['text_duration'] = 'المدة';
$_['text_timeout'] = 'انتهاء الوقت';

// الأمان والصلاحيات
$_['text_security'] = 'الأمان والصلاحيات';
$_['text_access_control'] = 'التحكم في الوصول';
$_['text_workflow_permissions'] = 'صلاحيات سير العمل';
$_['text_execution_permissions'] = 'صلاحيات التنفيذ';
$_['text_modification_permissions'] = 'صلاحيات التعديل';

// التكامل
$_['text_integrations'] = 'التكاملات';
$_['text_external_systems'] = 'الأنظمة الخارجية';
$_['text_api_integration'] = 'تكامل API';
$_['text_webhook_integration'] = 'تكامل Webhook';
$_['text_database_integration'] = 'تكامل قاعدة البيانات';

// Missing Variables from Audit Report
$_['button_cancel'] = '';
$_['button_design'] = '';
$_['button_save'] = '';
$_['button_setup'] = '';
$_['button_workflow_list'] = '';
$_['column_date_added'] = '';
$_['column_description'] = '';
$_['date_format_short'] = '';
$_['error_save'] = '';
$_['text_cancel'] = '';
$_['text_confirm_new'] = '';
$_['text_database_setup'] = '';
$_['text_disabled'] = '';
$_['text_enabled'] = '';
$_['text_error_saving'] = '';
$_['text_execution_instructions'] = '';
$_['text_fit'] = '';
$_['text_home'] = '';
$_['text_name'] = '';
$_['text_new'] = '';
$_['text_pagination'] = '';
$_['text_setup'] = '';
$_['text_workflow_designer'] = '';
$_['workflow/workflow'] = '';
$_['workflow_name'] = '';
?>
