<?php
// Heading
$_['heading_title']                    = 'Chart of Accounts';

// Text
$_['text_success']                     = 'Success: Chart of Accounts has been modified successfully!';
$_['text_list']                        = 'Account List';
$_['text_add']                         = 'Add Account';
$_['text_edit']                        = 'Edit Account';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_chart_of_accounts']           = 'Chart of Accounts';
$_['text_account_tree']                = 'Account Tree';
$_['text_account_balance']             = 'Account Balance';
$_['text_account_statement']           = 'Account Statement';
$_['text_parent_account']              = 'Parent Account';
$_['text_sub_accounts']                = 'Sub Accounts';

// Column
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_account_type']              = 'Account Type';
$_['column_account_nature']            = 'Account Nature';
$_['column_parent_account']            = 'Parent Account';
$_['column_level']                     = 'Level';
$_['column_balance']                   = 'Balance';
$_['column_debit_balance']             = 'Debit Balance';
$_['column_credit_balance']            = 'Credit Balance';
$_['column_status']                    = 'Status';
$_['column_action']                    = 'Action';
$_['column_sort_order']                = 'Sort Order';

// Entry
$_['entry_account_code']               = 'Account Code';
$_['entry_account_name']               = 'Account Name';
$_['entry_account_type']               = 'Account Type';
$_['entry_parent_account']             = 'Parent Account';
$_['entry_account_nature']             = 'Account Nature';
$_['entry_opening_balance']            = 'Opening Balance';
$_['entry_status']                     = 'Status';
$_['entry_sort_order']                 = 'Sort Order';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';

// Button
$_['button_add_account']               = 'Add Account';
$_['button_edit_account']              = 'Edit Account';
$_['button_delete_account']            = 'Delete Account';
$_['button_view_tree']                 = 'View Tree';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_import']                    = 'Import';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';

// Tab
$_['tab_general']                      = 'General';
$_['tab_data']                         = 'Data';
$_['tab_balance']                      = 'Balance';

// Help
$_['help_account_code']                = 'Enter unique account code (numbers only)';
$_['help_account_type']                = 'Select account type (Debit/Credit)';
$_['help_parent_account']              = 'Select parent account for hierarchical structure';
$_['help_opening_balance']             = 'Enter opening balance if any';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to modify Chart of Accounts!';
$_['error_account_code']               = 'Account Code is required and must be unique!';
$_['error_account_name']               = 'Account Name must be between 1 and 255 characters!';
$_['error_account_type']               = 'Please select account type!';
$_['error_parent_account']             = 'Please choose a parent account other than the one you selected!';
$_['error_duplicate_code']             = 'Account Code already exists!';
$_['error_has_children']               = 'Cannot delete account that has sub-accounts!';
$_['error_has_transactions']           = 'Cannot delete account that has transactions!';

// Success
$_['text_success_add']                 = 'Account added successfully!';
$_['text_success_edit']                = 'Account updated successfully!';
$_['text_success_delete']              = 'Account deleted successfully!';
$_['text_success_import']              = 'Accounts imported successfully!';
$_['text_success_export']              = 'Accounts exported successfully!';

// Account Types
$_['text_debit']                       = 'Debit';
$_['text_credit']                      = 'Credit';
$_['text_asset']                       = 'Asset';
$_['text_liability']                   = 'Liability';
$_['text_equity']                      = 'Equity';
$_['text_revenue']                     = 'Revenue';
$_['text_expense']                     = 'Expense';

// Tree View
$_['text_expand_all']                  = 'Expand All';
$_['text_collapse_all']                = 'Collapse All';
$_['text_show_balances']               = 'Show Balances';
$_['text_hide_balances']               = 'Hide Balances';

// Export/Import
$_['text_export_format']               = 'Export Format';
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';
$_['text_import_file']                 = 'Import File';
$_['text_download_template']           = 'Download Template';

// Print
$_['text_print_options']               = 'Print Options';
$_['text_include_balances']            = 'Include Balances';
$_['text_tree_format']                 = 'Tree Format';
$_['text_list_format']                 = 'List Format';

// Additional Fields
$_['text_print_date']                  = 'Print Date';
$_['column_current_balance']           = 'Current Balance';
$_['column_status']                    = 'Account Status';
$_['text_total']                       = 'Total';
$_['text_no_results']                  = 'No results found';
$_['error_excel_not_supported']        = 'Excel files are not currently supported';

// Success Messages
$_['success_account_added']            = 'Account added successfully!';
$_['success_account_updated']          = 'Account updated successfully!';
$_['success_account_deleted']          = 'Account deleted successfully!';
$_['success_balance_updated']          = 'Balance updated successfully!';

// Buttons
$_['button_view_statement']            = 'View Statement';
$_['button_update_balance']            = 'Update Balance';

// Advanced Filters
$_['text_filter_account_type']         = 'Account Type';
$_['text_filter_status']               = 'Status';
$_['text_filter_has_balance']          = 'Has Balance';
$_['text_filter_parent_account']       = 'Parent Account';
$_['text_filter_search']               = 'Search';

// Tree View
$_['text_tree_view']                   = 'Tree View';
$_['text_list_view']                   = 'List View';

// Tax Accounts
$_['text_add_tax_accounts']            = 'Add Tax Accounts';
$_['text_add_eta_accounts']            = 'Add ETA Accounts';
$_['text_tax_accounts_added']          = '%d tax accounts added successfully!';
$_['text_eta_accounts_added']          = '%d ETA accounts added successfully!';

// Missing Variables from Audit Report
$_['accounts/chartaccount'] = '';
$_['date_format_long'] = '';
$_['error_name'] = '';
$_['error_parent'] = '';
$_['text_home'] = '';
$_['text_import'] = '';
$_['text_pagination'] = '';
