<?php
// Heading
$_['heading_title'] = 'Journal Entries';

// Text
$_['text_success'] = 'Entry saved successfully!';
$_['text_success_add'] = 'Entry added successfully!';
$_['text_success_edit'] = 'Entry updated successfully!';
$_['text_success_delete'] = 'Entry deleted successfully!';
$_['text_success_post'] = 'Entry posted successfully!';
$_['text_success_unpost'] = 'Entry unposted successfully!';
$_['text_success_duplicate'] = 'Entry duplicated successfully!';
$_['text_success_template_save'] = 'Template saved successfully!';

$_['text_list'] = 'Entries List';
$_['text_add'] = 'Add Entry';
$_['text_edit'] = 'Edit Entry';
$_['text_view'] = 'View Entry';
$_['text_print'] = 'Print';
$_['text_print_selected'] = 'Print Selected';
$_['text_export'] = 'Export';
$_['text_import'] = 'Import';
$_['text_duplicate'] = 'Duplicate';
$_['text_post'] = 'Post';
$_['text_unpost'] = 'Unpost';
$_['text_draft'] = 'Draft';
$_['text_posted'] = 'Posted';
$_['text_cancelled'] = 'Cancelled';
$_['text_balanced'] = 'Balanced';
$_['text_unbalanced'] = 'Unbalanced';
$_['text_save_and_print'] = 'Save and Print';
$_['text_save_as_template'] = 'Save as Template';
$_['text_load_template'] = 'Load Template';
$_['text_debit_entries'] = 'Debit Entries';
$_['text_credit_entries'] = 'Credit Entries';
$_['text_general'] = 'General';
$_['text_auto'] = 'Auto';
$_['text_manual'] = 'Manual';
$_['text_recurring'] = 'Recurring';
$_['text_adjustment'] = 'Adjustment';
$_['text_closing'] = 'Closing';
$_['text_opening'] = 'Opening';
$_['text_reversal'] = 'Reversal';
$_['text_accrual'] = 'Accrual';
$_['text_reclassification'] = 'Reclassification';
$_['text_correction'] = 'Correction';

// Entry
$_['entry_date'] = 'Entry Date';
$_['entry_description'] = 'Description';
$_['entry_reference'] = 'Reference';
$_['entry_type'] = 'Entry Type';
$_['entry_status'] = 'Status';
$_['entry_account_code'] = 'Account Code';
$_['entry_account_name'] = 'Account Name';
$_['entry_debit'] = 'Debit';
$_['entry_credit'] = 'Credit';
$_['entry_amount'] = 'Amount';
$_['entry_narration'] = 'Narration';
$_['entry_branch'] = 'Branch';
$_['entry_cost_center'] = 'Cost Center';
$_['entry_project'] = 'Project';
$_['entry_currency'] = 'Currency';
$_['entry_exchange_rate'] = 'Exchange Rate';
$_['entry_attachment'] = 'Attachment';
$_['entry_notes'] = 'Notes';
$_['entry_template_name'] = 'Template Name';
$_['entry_recurring_frequency'] = 'Recurring Frequency';
$_['entry_next_date'] = 'Next Date';
$_['entry_end_date'] = 'End Date';

// Column
$_['column_date'] = 'Date';
$_['column_reference'] = 'Reference';
$_['column_description'] = 'Description';
$_['column_type'] = 'Type';
$_['column_debit'] = 'Debit';
$_['column_credit'] = 'Credit';
$_['column_balance'] = 'Balance';
$_['column_status'] = 'Status';
$_['column_created_by'] = 'Created By';
$_['column_created_date'] = 'Created Date';
$_['column_posted_by'] = 'Posted By';
$_['column_posted_date'] = 'Posted Date';
$_['column_action'] = 'Action';
$_['column_account_code'] = 'Account Code';
$_['column_account_name'] = 'Account Name';
$_['column_narration'] = 'Narration';
$_['column_amount'] = 'Amount';

// Button
$_['button_add_entry'] = 'Add Entry';
$_['button_edit_entry'] = 'Edit Entry';
$_['button_delete_entry'] = 'Delete Entry';
$_['button_duplicate_entry'] = 'Duplicate Entry';
$_['button_post_entry'] = 'Post Entry';
$_['button_unpost_entry'] = 'Unpost Entry';
$_['button_print_entry'] = 'Print Entry';
$_['button_export_entry'] = 'Export Entry';
$_['button_add_line'] = 'Add Line';
$_['button_remove_line'] = 'Remove Line';
$_['button_calculate'] = 'Calculate';
$_['button_balance_check'] = 'Balance Check';
$_['button_save'] = 'Save';
$_['button_save_and_new'] = 'Save and New';
$_['button_save_and_post'] = 'Save and Post';
$_['button_cancel'] = 'Cancel';
$_['button_back'] = 'Back';
$_['button_filter'] = 'Filter';
$_['button_clear_filter'] = 'Clear Filter';
$_['button_search'] = 'Search';
$_['button_reset'] = 'Reset';

// Tab
$_['tab_general'] = 'General';
$_['tab_lines'] = 'Entry Lines';
$_['tab_attachments'] = 'Attachments';
$_['tab_audit'] = 'Audit Trail';
$_['tab_recurring'] = 'Recurring';
$_['tab_approval'] = 'Approval';

// Help
$_['help_reference'] = 'Enter unique reference number for this entry';
$_['help_description'] = 'Brief description of the transaction';
$_['help_type'] = 'Select the type of journal entry';
$_['help_balance'] = 'Total debits must equal total credits';
$_['help_posting'] = 'Posted entries cannot be modified';
$_['help_template'] = 'Save frequently used entries as templates';
$_['help_recurring'] = 'Set up automatic recurring entries';

// Error
$_['error_warning'] = 'Warning: Please check the form carefully for errors!';
$_['error_permission'] = 'Warning: You do not have permission to modify journal entries!';

// Missing Variables from Audit Report
$_['accounts/journal'] = '';
$_['code'] = '';
$_['direction'] = '';
$_['entry_is_debit'] = '';
$_['error_date_required'] = '';
$_['error_delete'] = '';
$_['error_description_required'] = '';
$_['error_entries_required'] = '';
$_['error_save'] = '';
$_['error_unbalanced'] = '';
$_['lang'] = '';
$_['text_delete'] = '';
$_['text_home'] = '';
$_['text_no'] = '';
$_['text_updated'] = '';
$_['text_yes'] = '';
$_['error_permission_post'] = 'Warning: You do not have permission to post journal entries!';
$_['error_permission_delete'] = 'Warning: You do not have permission to delete journal entries!';
$_['error_date'] = 'Entry date is required!';
$_['error_date_future'] = 'Entry date cannot be in the future!';
$_['error_date_closed'] = 'Entry date is in a closed period!';
$_['error_description'] = 'Description is required!';
$_['error_reference'] = 'Reference is required!';
$_['error_reference_exists'] = 'Reference already exists!';
$_['error_balance'] = 'Entry must be balanced! Debit total must equal credit total.';
$_['error_no_lines'] = 'At least two entry lines are required!';
$_['error_account_required'] = 'Account is required for all lines!';
$_['error_amount_required'] = 'Amount is required for all lines!';
$_['error_amount_zero'] = 'Amount cannot be zero!';
$_['error_duplicate_account'] = 'Account cannot be used twice in the same entry!';
$_['error_posted_entry'] = 'Cannot modify posted entry!';
$_['error_entry_not_found'] = 'Entry not found!';
$_['error_template_name'] = 'Template name is required!';
$_['error_template_exists'] = 'Template name already exists!';

// Success Messages
$_['text_entry_saved'] = 'Journal entry saved successfully!';
$_['text_entry_posted'] = 'Journal entry posted successfully!';
$_['text_entry_deleted'] = 'Journal entry deleted successfully!';
$_['text_template_saved'] = 'Template saved successfully!';
$_['text_template_loaded'] = 'Template loaded successfully!';

// Status Messages
$_['text_status_draft'] = 'Draft';
$_['text_status_posted'] = 'Posted';
$_['text_status_cancelled'] = 'Cancelled';
$_['text_status_pending'] = 'Pending Approval';
$_['text_status_approved'] = 'Approved';
$_['text_status_rejected'] = 'Rejected';

// Filter Options
$_['text_filter_all'] = 'All Entries';
$_['text_filter_draft'] = 'Draft Entries';
$_['text_filter_posted'] = 'Posted Entries';
$_['text_filter_today'] = 'Today';
$_['text_filter_this_week'] = 'This Week';
$_['text_filter_this_month'] = 'This Month';
$_['text_filter_this_year'] = 'This Year';
$_['text_filter_custom'] = 'Custom Period';

// Export Options
$_['text_export_excel'] = 'Export to Excel';
$_['text_export_pdf'] = 'Export to PDF';
$_['text_export_csv'] = 'Export to CSV';

// Print Options
$_['text_print_entry'] = 'Print Entry';
$_['text_print_multiple'] = 'Print Multiple';
$_['text_print_preview'] = 'Print Preview';

// Validation Messages
$_['text_validating'] = 'Validating entry...';
$_['text_calculating'] = 'Calculating totals...';
$_['text_posting'] = 'Posting entry...';
$_['text_saving'] = 'Saving entry...';

// Totals
$_['text_total_debit'] = 'Total Debit';
$_['text_total_credit'] = 'Total Credit';
$_['text_difference'] = 'Difference';
$_['text_balanced_entry'] = 'Entry is balanced';
$_['text_unbalanced_entry'] = 'Entry is not balanced';

// Templates
$_['text_template_list'] = 'Template List';
$_['text_load_from_template'] = 'Load from Template';
$_['text_save_as_new_template'] = 'Save as New Template';
$_['text_update_template'] = 'Update Template';
$_['text_delete_template'] = 'Delete Template';

// Recurring Entries
$_['text_setup_recurring'] = 'Setup Recurring';
$_['text_frequency_monthly'] = 'Monthly';
$_['text_frequency_quarterly'] = 'Quarterly';
$_['text_frequency_yearly'] = 'Yearly';
$_['text_frequency_weekly'] = 'Weekly';
$_['text_frequency_daily'] = 'Daily';

// Audit Trail
$_['text_created_by'] = 'Created by';
$_['text_created_on'] = 'Created on';
$_['text_modified_by'] = 'Modified by';
$_['text_modified_on'] = 'Modified on';
$_['text_posted_by'] = 'Posted by';
$_['text_posted_on'] = 'Posted on';
$_['text_audit_trail'] = 'Audit Trail';
$_['text_no_audit_data'] = 'No audit data available';

// Approval Workflow
$_['text_submit_for_approval'] = 'Submit for Approval';
$_['text_approve_entry'] = 'Approve Entry';
$_['text_reject_entry'] = 'Reject Entry';
$_['text_approval_required'] = 'Approval Required';
$_['text_approved_by'] = 'Approved by';
$_['text_rejected_by'] = 'Rejected by';
$_['text_approval_notes'] = 'Approval Notes';

// Additional Messages
$_['error_no_journals_selected'] = 'No journals selected for printing!';
$_['text_multiple_journal_entries'] = 'Multiple Journal Entries';
$_['text_pdf_generated_successfully'] = 'PDF generated successfully!';

// Status Messages
$_['text_generating_pdf'] = 'Generating PDF...';
$_['text_processing_entries'] = 'Processing entries...';
$_['text_validating_balance'] = 'Validating balance...';

// Additional Errors
$_['error_method'] = 'Invalid request method!';
$_['error_no_data'] = 'No data found!';

// Additional Fields
$_['text_total'] = 'Total';
$_['text_no_results'] = 'No results found';
$_['text_duplicate'] = 'Duplicate';
$_['text_post'] = 'Post';
$_['text_unpost'] = 'Unpost';
$_['text_draft'] = 'Draft';
$_['text_posted'] = 'Posted';
$_['text_cancelled'] = 'Cancelled';

// Date Format
$_['date_format_short'] = 'd/m/Y';
$_['date_format_long'] = 'd F Y';
$_['date_format_time'] = 'd/m/Y H:i';

// Save Options
$_['button_save_and_new'] = 'Save and Add New';
$_['button_save_and_print'] = 'Save and Print';
$_['text_save_options'] = 'Save Options';

// Advanced Features
$_['text_templates'] = 'Templates';
$_['text_recurring'] = 'Recurring';
$_['text_workflow'] = 'Workflow';
$_['text_validation'] = 'Validation';
?>
