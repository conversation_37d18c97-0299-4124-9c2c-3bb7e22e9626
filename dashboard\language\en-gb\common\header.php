<?php
/**
 * AYM ERP Header Language File - English
 * ملف اللغة الإنجليزية للهيدر
 */

// Header Text
$_['text_notifications']               = 'Notifications';
$_['text_messages']                    = 'Messages';
$_['text_approvals']                   = 'Approvals';
$_['text_tasks']                       = 'Tasks';
$_['text_quick_actions']               = 'Quick Actions';
$_['text_profile']                     = 'Profile';
$_['text_settings']                    = 'Settings';
$_['text_logout']                      = 'Logout';
$_['text_loading']                     = 'Loading...';

// Notification System
$_['text_mark_all_as_read']            = 'Mark All as Read';
$_['text_view_all_notifications']      = 'View All Notifications';
$_['text_view_all_messages']           = 'View All Messages';
$_['text_view_all_approvals']          = 'View All Approvals';
$_['text_view_all_tasks']              = 'View All Tasks';
$_['text_no_notifications']            = 'No new notifications';
$_['text_no_messages']                 = 'No new messages';
$_['text_no_approvals']                = 'No new approvals';
$_['text_no_tasks']                    = 'No new tasks';

// Quick Actions
$_['text_create_invoice']              = 'Create Invoice';
$_['text_add_customer']                = 'Add Customer';
$_['text_create_task']                 = 'Create Task';
$_['text_add_product']                 = 'Add Product';
$_['text_create_order']                = 'Create Order';
$_['text_add_supplier']                = 'Add Supplier';
$_['text_create_report']               = 'Create Report';

// User Profile
$_['text_user_profile']                = 'User Profile';
$_['text_user_settings']               = 'User Settings';
$_['text_change_password']             = 'Change Password';
$_['text_preferences']                 = 'Preferences';
$_['text_activity_log']                = 'Activity Log';

// Logout
$_['text_confirm_logout']              = 'Confirm Logout';
$_['text_logout_message']              = 'Are you sure you want to logout?';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_cancel']                      = 'Cancel';

// Missing Variables from Audit Report
$_['base'] = '';
$_['code'] = '';
$_['common/header'] = '';
$_['description'] = '';
$_['direction'] = '';
$_['firstname'] = '';
$_['home'] = '';
$_['image'] = '';
$_['keywords'] = '';
$_['lang'] = '';
$_['lastname'] = '';
$_['logout'] = '';
$_['profile'] = '';
$_['script'] = '';
$_['title'] = '';
$_['user_token'] = '';
$_['username'] = '';

// Additional Header Variables for New Controller
$_['text_logged']                      = 'Logged in as %s';
$_['text_security']                    = 'Security';
$_['text_low_stock_alert']             = 'Low Stock Alert';
$_['text_product_low_stock']           = 'Product %s has low stock: %d remaining';
$_['text_expiry_alert']                = 'Expiry Alert';
$_['text_product_expiring']            = 'Product %s expires on %s';
$_['text_overdue_task']                = 'Overdue Task';
$_['text_task_overdue']                = 'Task %s was due on %s';
$_['text_upcoming_task']               = 'Upcoming Task';
$_['text_task_upcoming']               = 'Task %s is due on %s';
$_['text_notification_marked_read']    = 'Notification marked as read';

// Error Messages
$_['error_loading_header_data']        = 'Error loading header data';
$_['error_user_not_logged']            = 'User not logged in';
$_['error_marking_notification']       = 'Error marking notification';
$_['error_invalid_request']            = 'Invalid request';

// Messages
$_['text_success']                     = 'Success';
$_['text_error']                       = 'Error';
$_['text_warning']                     = 'Warning';
$_['text_info']                        = 'Information';

// Time
$_['text_just_now']                    = 'Just now';
$_['text_minutes_ago']                 = 'minutes ago';
$_['text_hours_ago']                   = 'hours ago';
$_['text_days_ago']                    = 'days ago';

// Priority Levels
$_['text_priority_high']               = 'High';
$_['text_priority_medium']             = 'Medium';
$_['text_priority_low']                = 'Low';

// Notification Types
$_['text_notification_order']          = 'New Order';
$_['text_notification_inventory']      = 'Inventory Alert';
$_['text_notification_approval']       = 'Approval Request';
$_['text_notification_system']         = 'System Alert';
$_['text_notification_message']        = 'New Message';

// Status
$_['text_status_pending']              = 'Pending';
$_['text_status_processing']           = 'Processing';
$_['text_status_completed']            = 'Completed';
$_['text_status_cancelled']            = 'Cancelled';
$_['text_status_approved']             = 'Approved';
$_['text_status_rejected']             = 'Rejected';

// Actions
$_['text_action_view']                 = 'View';
$_['text_action_edit']                 = 'Edit';
$_['text_action_delete']               = 'Delete';
$_['text_action_approve']              = 'Approve';
$_['text_action_reject']               = 'Reject';
$_['text_action_export']               = 'Export';
$_['text_action_print']                = 'Print';

// Search
$_['text_search']                      = 'Search';
$_['text_search_placeholder']          = 'Search in system...';
$_['text_search_results']              = 'Search Results';
$_['text_no_search_results']           = 'No search results';

// Help
$_['text_help']                        = 'Help';
$_['text_documentation']               = 'Documentation';
$_['text_support']                     = 'Support';
$_['text_contact_us']                  = 'Contact Us';

// Language
$_['text_language']                    = 'Language';
$_['text_arabic']                      = 'العربية';
$_['text_english']                     = 'English';

// Theme
$_['text_theme']                       = 'Theme';
$_['text_theme_light']                 = 'Light';
$_['text_theme_dark']                  = 'Dark';
$_['text_theme_auto']                  = 'Auto';

// Accessibility
$_['text_accessibility']               = 'Accessibility';
$_['text_increase_font']               = 'Increase Font';
$_['text_decrease_font']               = 'Decrease Font';
$_['text_high_contrast']               = 'High Contrast';
$_['text_screen_reader']               = 'Screen Reader';

// Updates
$_['text_updates']                     = 'Updates';
$_['text_check_updates']               = 'Check for Updates';
$_['text_install_updates']             = 'Install Updates';
$_['text_updates_available']           = 'Updates Available';
$_['text_no_updates']                  = 'No Updates';

// System
$_['text_system']                      = 'System';
$_['text_system_info']                 = 'System Information';
$_['text_system_status']               = 'System Status';
$_['text_system_health']               = 'System Health';
$_['text_system_logs']                 = 'System Logs';

// Security
$_['text_security']                    = 'Security';
$_['text_security_log']                = 'Security Log';
$_['text_login_history']               = 'Login History';
$_['text_security_settings']           = 'Security Settings';
$_['text_two_factor_auth']             = 'Two-Factor Authentication';

// Performance
$_['text_performance']                 = 'Performance';
$_['text_performance_monitor']         = 'Performance Monitor';
$_['text_system_resources']            = 'System Resources';
$_['text_optimization']                = 'Optimization';

// Backup
$_['text_backup']                      = 'Backup';
$_['text_create_backup']               = 'Create Backup';
$_['text_restore_backup']              = 'Restore Backup';
$_['text_backup_history']              = 'Backup History';

// Maintenance
$_['text_maintenance']                 = 'Maintenance';
$_['text_maintenance_mode']            = 'Maintenance Mode';
$_['text_cache_clear']                 = 'Clear Cache';
$_['text_database_optimize']           = 'Optimize Database';

// API
$_['text_api']                         = 'API';
$_['text_api_documentation']           = 'API Documentation';
$_['text_api_keys']                    = 'API Keys';
$_['text_api_logs']                    = 'API Logs';

// Integration
$_['text_integration']                 = 'Integration';
$_['text_third_party']                 = 'Third Party';
$_['text_webhooks']                    = 'Webhooks';
$_['text_import_export']               = 'Import/Export';

// Analytics
$_['text_analytics']                   = 'Analytics';
$_['text_usage_statistics']            = 'Usage Statistics';
$_['text_user_activity']               = 'User Activity';
$_['text_system_metrics']              = 'System Metrics';

// Reports
$_['text_reports']                     = 'Reports';
$_['text_generate_report']             = 'Generate Report';
$_['text_scheduled_reports']           = 'Scheduled Reports';
$_['text_report_templates']            = 'Report Templates';

// Audit
$_['text_audit']                       = 'Audit';
$_['text_audit_log']                   = 'Audit Log';
$_['text_audit_trail']                 = 'Audit Trail';
$_['text_compliance']                  = 'Compliance';

// Development
$_['text_development']                 = 'Development';
$_['text_developer_tools']             = 'Developer Tools';
$_['text_debug_mode']                  = 'Debug Mode';
$_['text_testing']                     = 'Testing';

// Error Messages
$_['error_connection']                 = 'Connection Error';
$_['error_permission']                 = 'Permission Error';
$_['error_validation']                 = 'Validation Error';
$_['error_system']                     = 'System Error';

// Success Messages
$_['success_saved']                    = 'Saved Successfully';
$_['success_updated']                  = 'Updated Successfully';
$_['success_deleted']                  = 'Deleted Successfully';
$_['success_exported']                 = 'Exported Successfully';

// Warning Messages
$_['warning_unsaved_changes']          = 'You have unsaved changes';
$_['warning_delete_confirmation']      = 'Are you sure you want to delete?';
$_['warning_system_maintenance']       = 'System is in maintenance mode';

// Info Messages
$_['info_processing']                  = 'Processing...';
$_['info_loading']                     = 'Loading...';
$_['info_no_data']                     = 'No data available';
$_['info_select_item']                 = 'Please select an item';

// Notification specific messages
$_['error_loading_notifications']      = 'Error loading notifications: ';
$_['error_updating_notification']      = 'Error updating notification: ';
$_['error_updating_notifications']     = 'Error updating notifications: ';
$_['error_invalid_data']               = 'Invalid data';
$_['text_message_from']                = 'Message from: ';
$_['text_approval_request']            = 'Approval request: ';
$_['text_from']                        = 'From: ';
$_['text_low_stock']                   = 'Low stock: ';
$_['text_current_stock']               = 'Current stock: ';
$_['text_minimum_limit']               = ' - Minimum: ';
$_['text_now']                         = 'Now';
$_['text_expiry_alert']                = 'Expiry alert: ';
$_['text_expires_in']                  = 'Expires in ';
$_['text_days']                        = ' days';
$_['text_overdue_task']                = 'Overdue task: ';
$_['text_overdue_by']                  = 'Overdue by ';
$_['text_upcoming_task']               = 'Upcoming task: ';
$_['text_due']                         = 'Due ';
$_['text_moments_ago']                 = 'Moments ago';
$_['text_all_notifications_marked_read'] = 'All notifications marked as read';
$_['text_notification_center']         = 'Notification Center';
$_['text_minutes_ago']                 = 'minutes ago';
$_['text_hours_ago']                   = 'hours ago';
$_['text_days_ago']                    = 'days ago';