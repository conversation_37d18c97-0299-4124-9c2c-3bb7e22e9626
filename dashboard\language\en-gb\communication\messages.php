<?php
/**
 * English Language File - Internal Messages System
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Internal Messages';

// General texts
$_['text_success'] = 'Success: You have modified messages!';
$_['text_list'] = 'Message List';
$_['text_add'] = 'Compose Message';
$_['text_edit'] = 'Edit Message';
$_['text_view'] = 'View Message';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No messages found!';
$_['text_loading'] = 'Loading...';

// Message boxes
$_['text_inbox'] = 'Inbox';
$_['text_sent'] = 'Sent Messages';
$_['text_drafts'] = 'Drafts';
$_['text_trash'] = 'Trash';
$_['text_archive'] = 'Archive';
$_['text_starred'] = 'Starred';
$_['text_important'] = 'Important';

// Message priorities
$_['text_priority_low'] = 'Low';
$_['text_priority_normal'] = 'Normal';
$_['text_priority_high'] = 'High';
$_['text_priority_urgent'] = 'Urgent';
$_['text_priority_critical'] = 'Critical';

// Message status
$_['text_status_unread'] = 'Unread';
$_['text_status_read'] = 'Read';
$_['text_status_replied'] = 'Replied';
$_['text_status_forwarded'] = 'Forwarded';
$_['text_status_archived'] = 'Archived';
$_['text_status_deleted'] = 'Deleted';

// Message types
$_['text_type_personal'] = 'Personal';
$_['text_type_official'] = 'Official';
$_['text_type_notification'] = 'Notification';
$_['text_type_announcement'] = 'Announcement';
$_['text_type_reminder'] = 'Reminder';
$_['text_type_request'] = 'Request';
$_['text_type_approval'] = 'Approval';

// Entry fields
$_['entry_to'] = 'To';
$_['entry_cc'] = 'CC';
$_['entry_bcc'] = 'BCC';
$_['entry_subject'] = 'Subject';
$_['entry_message'] = 'Message';
$_['entry_priority'] = 'Priority';
$_['entry_type'] = 'Message Type';
$_['entry_attachment'] = 'Attachments';
$_['entry_delivery_receipt'] = 'Delivery Receipt';
$_['entry_read_receipt'] = 'Read Receipt';

// Columns
$_['column_from'] = 'From';
$_['column_to'] = 'To';
$_['column_subject'] = 'Subject';
$_['column_priority'] = 'Priority';
$_['column_status'] = 'Status';
$_['column_date'] = 'Date';
$_['column_size'] = 'Size';
$_['column_attachments'] = 'Attachments';
$_['column_action'] = 'Action';

// Buttons
$_['button_compose'] = 'Compose';
$_['button_send'] = 'Send';
$_['button_save_draft'] = 'Save Draft';
$_['button_reply'] = 'Reply';
$_['button_reply_all'] = 'Reply All';
$_['button_forward'] = 'Forward';
$_['button_delete'] = 'Delete';
$_['button_archive'] = 'Archive';
$_['button_mark_read'] = 'Mark as Read';
$_['button_mark_unread'] = 'Mark as Unread';
$_['button_star'] = 'Star';
$_['button_unstar'] = 'Unstar';
$_['button_attach'] = 'Attach File';
$_['button_print'] = 'Print';
$_['button_export'] = 'Export';

// Search and filter
$_['text_search'] = 'Search';
$_['text_search_messages'] = 'Search Messages';
$_['text_advanced_search'] = 'Advanced Search';
$_['text_filter'] = 'Filter';
$_['text_filter_by_sender'] = 'Filter by Sender';
$_['text_filter_by_priority'] = 'Filter by Priority';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_filter_by_date'] = 'Filter by Date';
$_['text_filter_by_type'] = 'Filter by Type';

// Attachments
$_['text_attachments'] = 'Attachments';
$_['text_add_attachment'] = 'Add Attachment';
$_['text_remove_attachment'] = 'Remove Attachment';
$_['text_download_attachment'] = 'Download Attachment';
$_['text_view_attachment'] = 'View Attachment';
$_['text_attachment_size'] = 'Attachment Size';
$_['text_total_attachments'] = 'Total Attachments';
$_['text_max_attachment_size'] = 'Max Attachment Size';

// Statistics
$_['text_statistics'] = 'Statistics';
$_['text_total_messages'] = 'Total Messages';
$_['text_unread_messages'] = 'Unread Messages';
$_['text_sent_messages'] = 'Sent Messages';
$_['text_draft_messages'] = 'Draft Messages';
$_['text_archived_messages'] = 'Archived Messages';
$_['text_messages_today'] = 'Messages Today';
$_['text_messages_this_week'] = 'Messages This Week';
$_['text_messages_this_month'] = 'Messages This Month';

// Settings
$_['text_settings'] = 'Message Settings';
$_['text_general_settings'] = 'General Settings';
$_['text_notification_settings'] = 'Notification Settings';
$_['text_signature'] = 'Signature';
$_['text_auto_reply'] = 'Auto Reply';
$_['text_message_retention'] = 'Message Retention Period';
$_['text_max_message_size'] = 'Max Message Size';

// Signature
$_['text_signature_settings'] = 'Signature Settings';
$_['text_enable_signature'] = 'Enable Signature';
$_['text_signature_text'] = 'Signature Text';
$_['text_signature_html'] = 'HTML Signature';

// Auto reply
$_['text_auto_reply_settings'] = 'Auto Reply Settings';
$_['text_enable_auto_reply'] = 'Enable Auto Reply';
$_['text_auto_reply_subject'] = 'Auto Reply Subject';
$_['text_auto_reply_message'] = 'Auto Reply Message';
$_['text_auto_reply_start_date'] = 'Start Date';
$_['text_auto_reply_end_date'] = 'End Date';

// Notifications
$_['text_notifications'] = 'Notifications';
$_['text_email_notifications'] = 'Email Notifications';
$_['text_desktop_notifications'] = 'Desktop Notifications';
$_['text_mobile_notifications'] = 'Mobile Notifications';
$_['text_notification_sound'] = 'Notification Sound';

// Dates and times
$_['text_sent_at'] = 'Sent At';
$_['text_received_at'] = 'Received At';
$_['text_read_at'] = 'Read At';
$_['text_today'] = 'Today';
$_['text_yesterday'] = 'Yesterday';
$_['text_this_week'] = 'This Week';
$_['text_last_week'] = 'Last Week';
$_['text_this_month'] = 'This Month';
$_['text_last_month'] = 'Last Month';

// Bulk actions
$_['text_bulk_actions'] = 'Bulk Actions';
$_['text_select_all'] = 'Select All';
$_['text_select_none'] = 'Select None';
$_['text_bulk_delete'] = 'Delete Selected';
$_['text_bulk_archive'] = 'Archive Selected';
$_['text_bulk_mark_read'] = 'Mark Selected as Read';
$_['text_bulk_mark_unread'] = 'Mark Selected as Unread';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access messages!';
$_['error_to_required'] = 'To field is required!';
$_['error_subject'] = 'Subject must be between 3 and 255 characters!';
$_['error_message'] = 'Message must be between 1 and 10000 characters!';
$_['error_attachment_size'] = 'Attachment size is too large!';
$_['error_attachment_type'] = 'Attachment type is not supported!';
$_['error_recipient_not_found'] = 'Recipient not found!';
$_['error_message_not_found'] = 'Message not found!';
$_['error_access_denied'] = 'You do not have permission to access this message!';
$_['error_send_failed'] = 'Failed to send message!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this message?';
$_['text_confirm_bulk_delete'] = 'Are you sure you want to delete selected messages?';
$_['text_confirm_archive'] = 'Are you sure you want to archive this message?';
$_['text_confirm_send'] = 'Are you sure you want to send this message?';

// Help and tips
$_['help_to'] = 'Enter recipient names separated by comma';
$_['help_cc'] = 'Carbon copy to additional recipients';
$_['help_bcc'] = 'Blind carbon copy to recipients others cannot see';
$_['help_subject'] = 'Enter a clear and concise subject for the message';
$_['help_priority'] = 'Choose message priority';
$_['help_attachment'] = 'You can attach multiple files';

// Alerts
$_['alert_new_message'] = 'New Message';
$_['alert_message_sent'] = 'Message sent successfully';
$_['alert_message_saved'] = 'Draft saved';
$_['alert_message_deleted'] = 'Message deleted';
$_['alert_message_archived'] = 'Message archived';

// Export and print
$_['text_export_messages'] = 'Export Messages';
$_['text_export_format'] = 'Export Format';
$_['text_export_pdf'] = 'PDF';
$_['text_export_excel'] = 'Excel';
$_['text_export_csv'] = 'CSV';
$_['text_print_message'] = 'Print Message';

// Templates
$_['text_templates'] = 'Templates';
$_['text_message_templates'] = 'Message Templates';
$_['text_create_template'] = 'Create Template';
$_['text_use_template'] = 'Use Template';
$_['text_template_name'] = 'Template Name';
$_['text_template_content'] = 'Template Content';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_link_to_task'] = 'Link to Task';
$_['text_link_to_project'] = 'Link to Project';
$_['text_link_to_document'] = 'Link to Document';
$_['text_link_to_contact'] = 'Link to Contact';

// Security
$_['text_security'] = 'Security';
$_['text_encryption'] = 'Encryption';
$_['text_message_encryption'] = 'Message Encryption';
$_['text_secure_message'] = 'Secure Message';

// Custom folders
$_['text_folders'] = 'Folders';
$_['text_create_folder'] = 'Create Folder';
$_['text_folder_name'] = 'Folder Name';
$_['text_move_to_folder'] = 'Move to Folder';
$_['text_custom_folders'] = 'Custom Folders';

// Labels and tags
$_['text_labels'] = 'Labels';
$_['text_add_label'] = 'Add Label';
$_['text_remove_label'] = 'Remove Label';
$_['text_label_name'] = 'Label Name';
$_['text_label_color'] = 'Label Color';

// Follow-up
$_['text_follow_up'] = 'Follow Up';
$_['text_set_follow_up'] = 'Set Follow Up';
$_['text_follow_up_date'] = 'Follow Up Date';
$_['text_follow_up_reminder'] = 'Follow Up Reminder';
$_['text_overdue_follow_ups'] = 'Overdue Follow Ups';

// Notification messages
$_['text_new_message_subject'] = 'New message: ';

// Missing Variables from Audit Report
$_['action'] = '';
$_['attachments'] = '';
$_['back'] = '';
$_['button_cancel'] = '';
$_['button_save'] = '';
$_['cancel'] = '';
$_['column_left'] = '';
$_['communication/messages'] = '';
$_['compose'] = '';
$_['delete'] = '';
$_['deleted_messages'] = '';
$_['deleted_total'] = '';
$_['error_attachments'] = '';
$_['error_back'] = '';
$_['error_compose'] = '';
$_['error_delete'] = '';
$_['error_deleted_messages'] = '';
$_['error_deleted_total'] = '';
$_['error_forward'] = '';
$_['error_heading_title'] = '';
$_['error_inbox_messages'] = '';
$_['error_inbox_total'] = '';
$_['error_inbox_unread'] = '';
$_['error_message_types'] = '';
$_['error_priorities'] = '';
$_['error_reply'] = '';
$_['error_sent_messages'] = '';
$_['error_sent_total'] = '';
$_['error_star'] = '';
$_['error_starred_messages'] = '';
$_['error_starred_total'] = '';
$_['error_unstar'] = '';
$_['footer'] = '';
$_['forward'] = '';
$_['header'] = '';
$_['inbox_messages'] = '';
$_['inbox_total'] = '';
$_['inbox_unread'] = '';
$_['message_types'] = '';
$_['priorities'] = '';
$_['reply'] = '';
$_['sent_messages'] = '';
$_['sent_total'] = '';
$_['star'] = '';
$_['starred_messages'] = '';
$_['starred_total'] = '';
$_['text_all'] = '';
$_['text_apply'] = '';
$_['text_clear'] = '';
$_['text_filter'] = '';
$_['text_from'] = '';
$_['text_home'] = '';
$_['text_mark_as_read'] = '';
$_['text_mark_as_unread'] = '';
$_['text_pagination'] = '';
$_['text_refresh'] = '';
$_['text_search'] = '';
$_['text_select'] = '';
$_['text_to'] = '';
$_['unstar'] = '';
?>
