<?php
/**
 * English Language File - Notification Settings
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Notification Settings';

// General texts
$_['text_success'] = 'Success: You have modified notification settings!';
$_['text_list'] = 'Settings List';
$_['text_add'] = 'Add Setting';
$_['text_edit'] = 'Edit Setting';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_enabled'] = 'Enabled';
$_['text_disabled'] = 'Disabled';
$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';

// Tabs
$_['tab_general'] = 'General Settings';
$_['tab_email'] = 'Email';
$_['tab_sms'] = 'SMS';
$_['tab_push'] = 'Push Notifications';
$_['tab_in_app'] = 'In-App Notifications';
$_['tab_preferences'] = 'Personal Preferences';

// General settings
$_['text_general_settings'] = 'General Settings';
$_['entry_enable_notifications'] = 'Enable Notifications';
$_['entry_default_language'] = 'Default Language';
$_['entry_timezone'] = 'Timezone';
$_['entry_date_format'] = 'Date Format';
$_['entry_time_format'] = 'Time Format';
$_['entry_notification_retention'] = 'Notification Retention (Days)';
$_['entry_max_notifications_per_user'] = 'Max Notifications per User';

// Email settings
$_['text_email_settings'] = 'Email Settings';
$_['entry_enable_email_notifications'] = 'Enable Email Notifications';
$_['entry_smtp_host'] = 'SMTP Host';
$_['entry_smtp_port'] = 'SMTP Port';
$_['entry_smtp_username'] = 'SMTP Username';
$_['entry_smtp_password'] = 'SMTP Password';
$_['entry_smtp_encryption'] = 'SMTP Encryption';
$_['entry_from_email'] = 'From Email';
$_['entry_from_name'] = 'From Name';
$_['entry_reply_to'] = 'Reply To';
$_['entry_email_template'] = 'Email Template';

// SMS settings
$_['text_sms_settings'] = 'SMS Settings';
$_['entry_enable_sms_notifications'] = 'Enable SMS Notifications';
$_['entry_sms_provider'] = 'SMS Provider';
$_['entry_sms_api_key'] = 'API Key';
$_['entry_sms_api_secret'] = 'API Secret';
$_['entry_sms_sender_id'] = 'Sender ID';
$_['entry_sms_template'] = 'SMS Template';

// Push notification settings
$_['text_push_settings'] = 'Push Notification Settings';
$_['entry_enable_push_notifications'] = 'Enable Push Notifications';
$_['entry_push_service'] = 'Push Service';
$_['entry_firebase_server_key'] = 'Firebase Server Key';
$_['entry_firebase_sender_id'] = 'Firebase Sender ID';
$_['entry_push_sound'] = 'Notification Sound';
$_['entry_push_badge'] = 'Notification Badge';

// In-app notification settings
$_['text_in_app_settings'] = 'In-App Notification Settings';
$_['entry_enable_in_app_notifications'] = 'Enable In-App Notifications';
$_['entry_show_notification_popup'] = 'Show Notification Popup';
$_['entry_notification_sound'] = 'Notification Sound';
$_['entry_auto_mark_read'] = 'Auto Mark as Read';
$_['entry_notification_position'] = 'Notification Position';

// Notification types
$_['text_notification_types'] = 'Notification Types';
$_['text_system_notifications'] = 'System Notifications';
$_['text_user_notifications'] = 'User Notifications';
$_['text_admin_notifications'] = 'Admin Notifications';
$_['text_security_notifications'] = 'Security Notifications';
$_['text_workflow_notifications'] = 'Workflow Notifications';
$_['text_task_notifications'] = 'Task Notifications';
$_['text_message_notifications'] = 'Message Notifications';
$_['text_reminder_notifications'] = 'Reminder Notifications';

// Priority levels
$_['text_priority_levels'] = 'Priority Levels';
$_['text_priority_low'] = 'Low';
$_['text_priority_normal'] = 'Normal';
$_['text_priority_high'] = 'High';
$_['text_priority_urgent'] = 'Urgent';
$_['text_priority_critical'] = 'Critical';

// Notification channels
$_['text_notification_channels'] = 'Notification Channels';
$_['text_channel_email'] = 'Email';
$_['text_channel_sms'] = 'SMS';
$_['text_channel_push'] = 'Push';
$_['text_channel_in_app'] = 'In-App';
$_['text_channel_webhook'] = 'Webhook';

// Personal preferences
$_['text_personal_preferences'] = 'Personal Preferences';
$_['text_notification_frequency'] = 'Notification Frequency';
$_['text_frequency_immediate'] = 'Immediate';
$_['text_frequency_hourly'] = 'Hourly';
$_['text_frequency_daily'] = 'Daily';
$_['text_frequency_weekly'] = 'Weekly';
$_['text_frequency_never'] = 'Never';

// Notification schedule
$_['text_notification_schedule'] = 'Notification Schedule';
$_['entry_quiet_hours_start'] = 'Quiet Hours Start';
$_['entry_quiet_hours_end'] = 'Quiet Hours End';
$_['entry_weekend_notifications'] = 'Weekend Notifications';
$_['entry_holiday_notifications'] = 'Holiday Notifications';

// Filtering and grouping
$_['text_filtering_grouping'] = 'Filtering and Grouping';
$_['entry_group_similar_notifications'] = 'Group Similar Notifications';
$_['entry_filter_spam'] = 'Filter Spam';
$_['entry_max_notifications_per_hour'] = 'Max Notifications per Hour';
$_['entry_digest_mode'] = 'Digest Mode';

// Security and privacy
$_['text_security_privacy'] = 'Security and Privacy';
$_['entry_encrypt_notifications'] = 'Encrypt Notifications';
$_['entry_secure_delivery'] = 'Secure Delivery';
$_['entry_data_retention_policy'] = 'Data Retention Policy';
$_['entry_privacy_level'] = 'Privacy Level';

// Entry fields
$_['entry_notification_type'] = 'Notification Type';
$_['entry_priority'] = 'Priority';
$_['entry_channels'] = 'Channels';
$_['entry_template'] = 'Template';
$_['entry_conditions'] = 'Conditions';
$_['entry_recipients'] = 'Recipients';

// Columns
$_['column_type'] = 'Type';
$_['column_priority'] = 'Priority';
$_['column_channels'] = 'Channels';
$_['column_status'] = 'Status';
$_['column_created'] = 'Created';
$_['column_action'] = 'Action';

// Buttons
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';
$_['button_test'] = 'Test';
$_['button_reset'] = 'Reset';
$_['button_export'] = 'Export';
$_['button_import'] = 'Import';

// Testing
$_['text_test_notifications'] = 'Test Notifications';
$_['text_send_test_email'] = 'Send Test Email';
$_['text_send_test_sms'] = 'Send Test SMS';
$_['text_send_test_push'] = 'Send Test Push';
$_['entry_test_recipient'] = 'Test Recipient';
$_['entry_test_message'] = 'Test Message';

// Statistics
$_['text_statistics'] = 'Statistics';
$_['text_notifications_sent'] = 'Notifications Sent';
$_['text_notifications_delivered'] = 'Notifications Delivered';
$_['text_notifications_failed'] = 'Notifications Failed';
$_['text_delivery_rate'] = 'Delivery Rate';
$_['text_open_rate'] = 'Open Rate';
$_['text_click_rate'] = 'Click Rate';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access notification settings!';
$_['error_smtp_host'] = 'SMTP Host is required!';
$_['error_smtp_port'] = 'SMTP Port is required!';
$_['error_from_email'] = 'From Email is required!';
$_['error_sms_provider'] = 'SMS Provider is required!';
$_['error_api_key'] = 'API Key is required!';
$_['error_invalid_email'] = 'Invalid email address!';
$_['error_invalid_phone'] = 'Invalid phone number!';
$_['error_test_failed'] = 'Test failed to send!';

// Success messages
$_['success_settings_saved'] = 'Settings saved successfully!';
$_['success_test_sent'] = 'Test sent successfully!';
$_['success_settings_reset'] = 'Settings reset successfully!';

// Help and tips
$_['help_smtp_host'] = 'Enter your SMTP server address';
$_['help_smtp_port'] = 'Default port is 587 for TLS or 465 for SSL';
$_['help_encryption'] = 'Choose encryption type supported by your server';
$_['help_api_key'] = 'Get API key from your service provider dashboard';
$_['help_sender_id'] = 'Sender ID that will appear to recipients';
$_['help_quiet_hours'] = 'Time period when notifications will not be sent';

// Alerts
$_['alert_unsaved_changes'] = 'You have unsaved changes!';
$_['alert_test_mode'] = 'You are in test mode';
$_['alert_production_mode'] = 'You are in production mode';

// Status
$_['text_status_active'] = 'Active';
$_['text_status_inactive'] = 'Inactive';
$_['text_status_testing'] = 'Testing';
$_['text_status_maintenance'] = 'Maintenance';

// Export and import
$_['text_export_settings'] = 'Export Settings';
$_['text_import_settings'] = 'Import Settings';
$_['text_backup_settings'] = 'Backup Settings';
$_['text_restore_settings'] = 'Restore Settings';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_third_party_services'] = 'Third Party Services';
$_['text_webhook_settings'] = 'Webhook Settings';
$_['text_api_settings'] = 'API Settings';

// Monitoring and logs
$_['text_monitoring'] = 'Monitoring';
$_['text_logs'] = 'Logs';
$_['text_error_logs'] = 'Error Logs';
$_['text_delivery_logs'] = 'Delivery Logs';
$_['text_performance_metrics'] = 'Performance Metrics';

// Maintenance
$_['text_maintenance'] = 'Maintenance';
$_['text_cleanup_old_notifications'] = 'Cleanup Old Notifications';
$_['text_optimize_database'] = 'Optimize Database';
$_['text_clear_cache'] = 'Clear Cache';
$_['text_rebuild_index'] = 'Rebuild Index';

// Missing Variables from Audit Report
$_['action'] = '';
$_['cancel'] = '';
$_['column_left'] = '';
$_['error_batch_interval'] = '';
$_['error_invalid_file'] = '';
$_['error_no_file'] = '';
$_['error_warning'] = '';
$_['footer'] = '';
$_['header'] = '';
$_['notification/settings'] = '';
$_['success'] = '';
$_['tab_advanced'] = '';
$_['tab_option'] = '';
$_['text_home'] = '';
$_['text_import_success'] = '';
$_['text_test_failed'] = '';
$_['text_test_success'] = '';
$_['user_token'] = '';
?>
