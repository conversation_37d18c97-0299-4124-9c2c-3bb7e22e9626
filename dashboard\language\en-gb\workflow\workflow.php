<?php
/**
 * English Language File - Workflow Management
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Workflow Management';
$_['heading_title_designer'] = 'Workflow Designer';
$_['heading_title_setup'] = 'Workflow Database Setup';

// Text
$_['text_success']            = 'Success: You have modified workflows!';
$_['text_success_save']       = 'Success: Workflow saved successfully!';
$_['text_list']               = 'Workflow List';
$_['text_add']                = 'Add Workflow';
$_['text_edit']               = 'Edit Workflow';
$_['text_enabled']            = 'Enabled';
$_['text_disabled']           = 'Disabled';
$_['text_setup']              = 'Database Setup';
$_['text_database_setup']     = 'Workflow Database Setup';
$_['text_setup_info']         = 'The workflow module needs database tables to be created. Below is the SQL to create these tables:';
$_['text_workflow_tables']    = 'Workflow Tables';
$_['text_execution_instructions'] = 'Run these SQL queries in your database management tool to create the required tables for the workflow module.';
$_['text_new_workflow']       = 'New Workflow';
$_['text_workflow_designer']  = 'Workflow Designer';

// Workflow Designer Texts
$_['text_name']               = 'Workflow Name';
$_['text_description']        = 'Description';
$_['text_save']               = 'Save';
$_['text_new']                = 'New';
$_['text_delete']             = 'Delete';
$_['text_properties']         = 'Properties';
$_['text_apply']              = 'Apply';
$_['text_nodes']              = 'Node Types';
$_['text_node_start']         = 'Start';
$_['text_node_end']           = 'End';
$_['text_node_task']          = 'Task';
$_['text_node_decision']      = 'Decision';
$_['text_node_email']         = 'Email';
$_['text_node_delay']         = 'Delay';
$_['text_zoom_in']            = 'Zoom In';
$_['text_zoom_out']           = 'Zoom Out';
$_['text_fit']                = 'Fit to View';
$_['text_cancel']             = 'Cancel';
$_['text_confirm_delete']     = 'Are you sure you want to delete this workflow? This action cannot be undone.';
$_['text_confirm_new']        = 'Are you sure you want to create a new workflow? Any unsaved changes will be lost.';
$_['text_workflow_saved']     = 'Workflow saved successfully';
$_['text_error_saving']       = 'Error saving workflow';

// Column
$_['column_name']             = 'Name';
$_['column_description']      = 'Description';
$_['column_status']           = 'Status';
$_['column_date_added']       = 'Date Added';
$_['column_action']           = 'Action';

// Entry
$_['entry_name']              = 'Workflow Name';
$_['entry_description']       = 'Description';
$_['entry_status']            = 'Status';
$_['entry_workflow_designer'] = 'Workflow Designer';

// Help
$_['help_designer']           = 'Use the visual workflow designer to create and edit your workflow.';

// Text for form
$_['text_save_first']         = 'Please save the workflow first to access the visual designer.';

// Button
$_['button_save']             = 'Save';
$_['button_cancel']           = 'Cancel';
$_['button_add']              = 'Add Workflow';
$_['button_edit']             = 'Edit';
$_['button_delete']           = 'Delete';
$_['button_design']           = 'Design Workflow';
$_['button_setup']            = 'Database Setup';
$_['button_workflow_list']    = 'Workflow List';

// Workflow status
$_['text_status_draft'] = 'Draft';
$_['text_status_active'] = 'Active';
$_['text_status_inactive'] = 'Inactive';
$_['text_status_paused'] = 'Paused';
$_['text_status_completed'] = 'Completed';
$_['text_status_cancelled'] = 'Cancelled';
$_['text_status_archived'] = 'Archived';
$_['text_status_testing'] = 'Testing';

// Workflow types
$_['text_type_approval'] = 'Approval';
$_['text_type_review'] = 'Review';
$_['text_type_notification'] = 'Notification';
$_['text_type_automation'] = 'Automation';
$_['text_type_escalation'] = 'Escalation';
$_['text_type_sequential'] = 'Sequential';
$_['text_type_parallel'] = 'Parallel';
$_['text_type_conditional'] = 'Conditional';

// Workflow priorities
$_['text_priority_low'] = 'Low';
$_['text_priority_normal'] = 'Normal';
$_['text_priority_high'] = 'High';
$_['text_priority_urgent'] = 'Urgent';
$_['text_priority_critical'] = 'Critical';

// Additional entry fields
$_['entry_type'] = 'Workflow Type';
$_['entry_category'] = 'Category';
$_['entry_priority'] = 'Priority';
$_['entry_owner'] = 'Owner';
$_['entry_trigger'] = 'Trigger';
$_['entry_conditions'] = 'Conditions';
$_['entry_actions'] = 'Actions';
$_['entry_timeout'] = 'Timeout (minutes)';
$_['entry_escalation'] = 'Escalation';
$_['entry_notification'] = 'Notifications';

// Additional columns
$_['column_type'] = 'Type';
$_['column_category'] = 'Category';
$_['column_priority'] = 'Priority';
$_['column_owner'] = 'Owner';
$_['column_last_run'] = 'Last Run';
$_['column_runs_count'] = 'Run Count';
$_['column_success_rate'] = 'Success Rate';

// Additional buttons
$_['button_activate'] = 'Activate';
$_['button_deactivate'] = 'Deactivate';
$_['button_pause'] = 'Pause';
$_['button_resume'] = 'Resume';
$_['button_test'] = 'Test';
$_['button_run'] = 'Run';
$_['button_logs'] = 'Logs';
$_['button_statistics'] = 'Statistics';

// Request management
$_['text_requests'] = 'Workflow Requests';
$_['text_active_requests'] = 'Active Requests';
$_['text_pending_requests'] = 'Pending Requests';
$_['text_completed_requests'] = 'Completed Requests';
$_['text_failed_requests'] = 'Failed Requests';
$_['text_cancelled_requests'] = 'Cancelled Requests';

// Request status
$_['text_request_status_new'] = 'New';
$_['text_request_status_in_progress'] = 'In Progress';
$_['text_request_status_waiting'] = 'Waiting';
$_['text_request_status_approved'] = 'Approved';
$_['text_request_status_rejected'] = 'Rejected';
$_['text_request_status_completed'] = 'Completed';
$_['text_request_status_failed'] = 'Failed';
$_['text_request_status_cancelled'] = 'Cancelled';
$_['text_request_status_escalated'] = 'Escalated';

// Statistics
$_['text_statistics'] = 'Statistics';
$_['text_execution_statistics'] = 'Execution Statistics';
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_success_rate'] = 'Success Rate';
$_['text_failure_rate'] = 'Failure Rate';
$_['text_average_duration'] = 'Average Duration';
$_['text_total_executions'] = 'Total Executions';
$_['text_active_instances'] = 'Active Instances';

// Search and filter
$_['text_search'] = 'Search';
$_['text_search_workflows'] = 'Search Workflows';
$_['text_filter'] = 'Filter';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_filter_by_type'] = 'Filter by Type';
$_['text_filter_by_category'] = 'Filter by Category';
$_['text_filter_by_owner'] = 'Filter by Owner';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to modify workflows!';
$_['error_name'] = 'Workflow name must be between 3 and 255 characters!';
$_['error_warning'] = 'Warning: Please check the form carefully for errors!';
$_['error_save'] = 'Error: Could not save workflow data!';
$_['error_active_instances'] = 'Warning: Cannot delete workflows with active instances!';
$_['error_type'] = 'Workflow type must be selected!';
$_['error_category'] = 'Workflow category must be selected!';
$_['error_owner'] = 'Workflow owner must be selected!';
$_['error_no_steps'] = 'At least one step must be added!';
$_['error_invalid_step'] = 'Invalid step configuration!';
$_['error_circular_reference'] = 'Circular reference not allowed!';
$_['error_workflow_running'] = 'Cannot modify workflow while running!';
$_['error_execution_failed'] = 'Workflow execution failed!';

// Confirmation messages
$_['text_confirm_activate'] = 'Are you sure you want to activate this workflow?';
$_['text_confirm_deactivate'] = 'Are you sure you want to deactivate this workflow?';
$_['text_confirm_run'] = 'Are you sure you want to run this workflow?';

// Help and tips
$_['help_name'] = 'Enter a clear and distinctive name for the workflow';
$_['help_description'] = 'Brief description of workflow purpose and function';
$_['help_type'] = 'Choose the appropriate workflow type';
$_['help_trigger'] = 'Define how the workflow should be triggered';
$_['help_conditions'] = 'Set conditions required for workflow execution';

// Alerts
$_['alert_workflow_created'] = 'Workflow created successfully';
$_['alert_workflow_updated'] = 'Workflow updated successfully';
$_['alert_workflow_deleted'] = 'Workflow deleted';
$_['alert_workflow_activated'] = 'Workflow activated';
$_['alert_workflow_deactivated'] = 'Workflow deactivated';
$_['alert_workflow_executed'] = 'Workflow executed successfully';

// Dates and times
$_['text_created_at'] = 'Created At';
$_['text_updated_at'] = 'Updated At';
$_['text_last_run_at'] = 'Last Run';
$_['text_next_run_at'] = 'Next Run';
$_['text_duration'] = 'Duration';
$_['text_timeout'] = 'Timeout';

// Security and permissions
$_['text_security'] = 'Security & Permissions';
$_['text_access_control'] = 'Access Control';
$_['text_workflow_permissions'] = 'Workflow Permissions';
$_['text_execution_permissions'] = 'Execution Permissions';
$_['text_modification_permissions'] = 'Modification Permissions';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_external_systems'] = 'External Systems';
$_['text_api_integration'] = 'API Integration';
$_['text_webhook_integration'] = 'Webhook Integration';
$_['text_database_integration'] = 'Database Integration';

// Workflow steps
$_['text_steps'] = 'Workflow Steps';
$_['text_step_details'] = 'Step Details';
$_['text_step_name'] = 'Step Name';
$_['text_step_type'] = 'Step Type';
$_['text_step_order'] = 'Step Order';
$_['text_step_conditions'] = 'Step Conditions';
$_['text_step_actions'] = 'Step Actions';
$_['text_step_timeout'] = 'Step Timeout';
$_['text_step_assignee'] = 'Step Assignee';

// Step types
$_['text_step_type_start'] = 'Start';
$_['text_step_type_approval'] = 'Approval';
$_['text_step_type_review'] = 'Review';
$_['text_step_type_notification'] = 'Notification';
$_['text_step_type_task'] = 'Task';
$_['text_step_type_condition'] = 'Condition';
$_['text_step_type_loop'] = 'Loop';
$_['text_step_type_parallel'] = 'Parallel';
$_['text_step_type_merge'] = 'Merge';
$_['text_step_type_end'] = 'End';

// Triggers
$_['text_triggers'] = 'Triggers';
$_['text_trigger_manual'] = 'Manual';
$_['text_trigger_automatic'] = 'Automatic';
$_['text_trigger_scheduled'] = 'Scheduled';
$_['text_trigger_event'] = 'Event';
$_['text_trigger_condition'] = 'Condition';
$_['text_trigger_webhook'] = 'Webhook';
$_['text_trigger_email'] = 'Email';
$_['text_trigger_api'] = 'API';

// Conditions
$_['text_conditions'] = 'Conditions';
$_['text_condition_field'] = 'Field';
$_['text_condition_operator'] = 'Operator';
$_['text_condition_value'] = 'Value';
$_['text_condition_logic'] = 'Logic';
$_['text_condition_and'] = 'AND';
$_['text_condition_or'] = 'OR';
$_['text_condition_not'] = 'NOT';

// Operators
$_['text_operator_equals'] = 'Equals';
$_['text_operator_not_equals'] = 'Not Equals';
$_['text_operator_greater'] = 'Greater Than';
$_['text_operator_greater_equal'] = 'Greater Than or Equal';
$_['text_operator_less'] = 'Less Than';
$_['text_operator_less_equal'] = 'Less Than or Equal';
$_['text_operator_contains'] = 'Contains';
$_['text_operator_not_contains'] = 'Does Not Contain';
$_['text_operator_starts_with'] = 'Starts With';
$_['text_operator_ends_with'] = 'Ends With';
$_['text_operator_is_empty'] = 'Is Empty';
$_['text_operator_is_not_empty'] = 'Is Not Empty';

// Actions
$_['text_actions'] = 'Actions';
$_['text_action_send_email'] = 'Send Email';
$_['text_action_send_sms'] = 'Send SMS';
$_['text_action_send_notification'] = 'Send Notification';
$_['text_action_update_field'] = 'Update Field';
$_['text_action_create_record'] = 'Create Record';
$_['text_action_delete_record'] = 'Delete Record';
$_['text_action_call_webhook'] = 'Call Webhook';
$_['text_action_run_script'] = 'Run Script';
$_['text_action_assign_task'] = 'Assign Task';
$_['text_action_escalate'] = 'Escalate';

// Escalation
$_['text_escalation'] = 'Escalation';
$_['text_escalation_enabled'] = 'Enable Escalation';
$_['text_escalation_after'] = 'Escalate After (minutes)';
$_['text_escalation_to'] = 'Escalate To';
$_['text_escalation_action'] = 'Escalation Action';
$_['text_escalation_levels'] = 'Escalation Levels';

// Notifications
$_['text_notifications'] = 'Notifications';
$_['text_notification_on_start'] = 'Notify on Start';
$_['text_notification_on_complete'] = 'Notify on Complete';
$_['text_notification_on_error'] = 'Notify on Error';
$_['text_notification_on_timeout'] = 'Notify on Timeout';
$_['text_notification_recipients'] = 'Notification Recipients';

// Variables
$_['text_variables'] = 'Variables';
$_['text_workflow_variables'] = 'Workflow Variables';
$_['text_system_variables'] = 'System Variables';
$_['text_user_variables'] = 'User Variables';
$_['text_custom_variables'] = 'Custom Variables';

// Logs and reports
$_['text_logs'] = 'Logs';
$_['text_execution_logs'] = 'Execution Logs';
$_['text_error_logs'] = 'Error Logs';
$_['text_audit_logs'] = 'Audit Logs';
$_['text_performance_logs'] = 'Performance Logs';

// Export and import
$_['text_export'] = 'Export';
$_['text_import'] = 'Import';
$_['text_export_workflow'] = 'Export Workflow';
$_['text_import_workflow'] = 'Import Workflow';
$_['text_export_format'] = 'Export Format';
$_['text_import_format'] = 'Import Format';

// Missing Variables from Audit Report
$_['button_cancel'] = '';
$_['button_design'] = '';
$_['button_save'] = '';
$_['button_setup'] = '';
$_['button_workflow_list'] = '';
$_['column_date_added'] = '';
$_['column_description'] = '';
$_['date_format_short'] = '';
$_['error_save'] = '';
$_['text_cancel'] = '';
$_['text_confirm_new'] = '';
$_['text_error_saving'] = '';
$_['text_fit'] = '';
$_['text_home'] = '';
$_['text_confirm'] = '';
$_['text_no_results'] = '';
$_['text_pagination'] = '';
$_['workflow/workflow'] = '';
$_['workflow_name'] = '';